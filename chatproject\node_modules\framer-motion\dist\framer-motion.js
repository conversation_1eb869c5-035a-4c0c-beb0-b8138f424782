!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}function i(t){if(t&&t.__esModule)return t;var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var s=i(e),o=n(e);const r=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),a=e.createContext({}),l=e.createContext(null),u="undefined"!=typeof document,c=u?e.useLayoutEffect:e.useEffect,h=e.createContext({strict:!1}),d=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+d("framerAppearId");function m(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function f(t){return"string"==typeof t||Array.isArray(t)}function g(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}const y=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],v=["initial",...y];function x(t){return g(t.animate)||v.some(e=>f(t[e]))}function P(t){return Boolean(x(t)||t.variants)}function w(t){const{initial:n,animate:i}=function(t,e){if(x(t)){const{initial:e,animate:n}=t;return{initial:!1===e||f(e)?e:void 0,animate:f(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(a));return e.useMemo(()=>({initial:n,animate:i}),[E(n),E(i)])}function E(t){return Array.isArray(t)?t.join(" "):t}const b={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},S={};for(const t in b)S[t]={isEnabled:e=>b[t].some(t=>!!e[t])};function T(t){for(const e in t)S[e]={...S[e],...t[e]}}const A=e.createContext({}),C=e.createContext({}),V=Symbol.for("motionComponentSymbol");function M({preloadedFeatures:t,createVisualElement:n,useRender:i,useVisualState:o,Component:d}){t&&T(t);const f=e.forwardRef((function(f,g){let y;const v={...e.useContext(r),...f,layoutId:D(f)},{isStatic:x}=v,P=w(f),E=o(f,x);if(!x&&u){P.visualElement=function(t,n,i,s){const{visualElement:o}=e.useContext(a),u=e.useContext(h),d=e.useContext(l),m=e.useContext(r).reducedMotion,f=e.useRef();s=s||u.renderer,!f.current&&s&&(f.current=s(t,{visualState:n,parent:o,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:m}));const g=f.current;e.useInsertionEffect(()=>{g&&g.update(i,d)});const y=e.useRef(Boolean(i[p]&&!window.HandoffComplete));return c(()=>{g&&(g.render(),y.current&&g.animationState&&g.animationState.animateChanges())}),e.useEffect(()=>{g&&(g.updateFeatures(),!y.current&&g.animationState&&g.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),g}(d,E,v,n);const i=e.useContext(C),s=e.useContext(h).strict;P.visualElement&&(y=P.visualElement.loadFeatures(v,s,t,i))}return s.createElement(a.Provider,{value:P},y&&P.visualElement?s.createElement(y,{visualElement:P.visualElement,...v}):null,i(d,f,function(t,n,i){return e.useCallback(e=>{e&&t.mount&&t.mount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):m(i)&&(i.current=e))},[n])}(E,P.visualElement,g),E,x,P.visualElement))}));return f[V]=d,f}function D({layoutId:t}){const n=e.useContext(A).id;return n&&void 0!==t?n+"-"+t:t}function R(t){function e(e,n={}){return M(t(e,n))}if("undefined"==typeof Proxy)return e;const n=new Map;return new Proxy(e,{get:(t,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const L=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function k(t){return"string"==typeof t&&!t.includes("-")&&!!(L.indexOf(t)>-1||/[A-Z]/.test(t))}const B={};function j(t){Object.assign(B,t)}const F=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],O=new Set(F);function I(t,{layout:e,layoutId:n}){return O.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!B[t]||"opacity"===t)}const U=t=>Boolean(t&&t.getVelocity),W={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},N=F.length;function z(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},i,s){let o="";for(let e=0;e<N;e++){const n=F[e];if(void 0!==t[n]){o+=`${W[n]||n}(${t[n]}) `}}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),s?o=s(t,i?"":o):n&&i&&(o="none"),o}const H=t=>e=>"string"==typeof e&&e.startsWith(t),$=H("--"),Y=H("var(--"),X=(t,e)=>e&&"number"==typeof t?e.transform(t):t,G=(t,e,n)=>Math.min(Math.max(n,t),e),q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Z={...q,transform:t=>G(0,1,t)},K={...q,default:1},_=t=>Math.round(1e5*t)/1e5,J=/(-)?([\d]*\.?[\d])+/g,Q=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,tt=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function et(t){return"string"==typeof t}const nt=t=>({test:e=>et(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),it=nt("deg"),st=nt("%"),ot=nt("px"),rt=nt("vh"),at=nt("vw"),lt={...st,parse:t=>st.parse(t)/100,transform:t=>st.transform(100*t)},ut={...q,transform:Math.round},ct={borderWidth:ot,borderTopWidth:ot,borderRightWidth:ot,borderBottomWidth:ot,borderLeftWidth:ot,borderRadius:ot,radius:ot,borderTopLeftRadius:ot,borderTopRightRadius:ot,borderBottomRightRadius:ot,borderBottomLeftRadius:ot,width:ot,maxWidth:ot,height:ot,maxHeight:ot,size:ot,top:ot,right:ot,bottom:ot,left:ot,padding:ot,paddingTop:ot,paddingRight:ot,paddingBottom:ot,paddingLeft:ot,margin:ot,marginTop:ot,marginRight:ot,marginBottom:ot,marginLeft:ot,rotate:it,rotateX:it,rotateY:it,rotateZ:it,scale:K,scaleX:K,scaleY:K,scaleZ:K,skew:it,skewX:it,skewY:it,distance:ot,translateX:ot,translateY:ot,translateZ:ot,x:ot,y:ot,z:ot,perspective:ot,transformPerspective:ot,opacity:Z,originX:lt,originY:lt,originZ:ot,zIndex:ut,fillOpacity:Z,strokeOpacity:Z,numOctaves:ut};function ht(t,e,n,i){const{style:s,vars:o,transform:r,transformOrigin:a}=t;let l=!1,u=!1,c=!0;for(const t in e){const n=e[t];if($(t)){o[t]=n;continue}const i=ct[t],h=X(n,i);if(O.has(t)){if(l=!0,r[t]=h,!c)continue;n!==(i.default||0)&&(c=!1)}else t.startsWith("origin")?(u=!0,a[t]=h):s[t]=h}if(e.transform||(l||i?s.transform=z(t.transform,n,c,i):s.transform&&(s.transform="none")),u){const{originX:t="50%",originY:e="50%",originZ:n=0}=a;s.transformOrigin=`${t} ${e} ${n}`}}const dt=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function pt(t,e,n){for(const i in e)U(e[i])||I(i,n)||(t[i]=e[i])}function mt(t,n,i){const s={};return pt(s,t.style||{},t),Object.assign(s,function({transformTemplate:t},n,i){return e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return ht(e,n,{enableHardwareAcceleration:!i},t),Object.assign({},e.vars,e.style)},[n])}(t,n,i)),t.transformValues?t.transformValues(s):s}function ft(t,e,n){const i={},s=mt(t,e,n);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i}const gt=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function yt(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||gt.has(t)}let vt=t=>!yt(t);function xt(t){t&&(vt=e=>e.startsWith("on")?!yt(e):t(e))}try{xt(require("@emotion/is-prop-valid").default)}catch(t){}function Pt(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(vt(s)||!0===n&&yt(s)||!e&&!yt(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}function wt(t,e,n){return"string"==typeof t?t:ot.transform(e+n*t)}const Et={offset:"stroke-dashoffset",array:"stroke-dasharray"},bt={offset:"strokeDashoffset",array:"strokeDasharray"};function St(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h,d){if(ht(t,u,c,d),h)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==s||void 0!==o||m.transform)&&(m.transformOrigin=function(t,e,n){return`${wt(e,t.x,t.width)} ${wt(n,t.y,t.height)}`}(f,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(p.x=e),void 0!==n&&(p.y=n),void 0!==i&&(p.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Et:bt;t[o.offset]=ot.transform(-i);const r=ot.transform(e),a=ot.transform(n);t[o.array]=`${r} ${a}`}(p,r,a,l,!1)}const Tt=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),At=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Ct(t,n,i,s){const o=e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return St(e,n,{enableHardwareAcceleration:!1},At(s),t.transformTemplate),{...e.attrs,style:{...e.style}}},[n]);if(t.style){const e={};pt(e,t.style,t),o.style={...e,...o.style}}return o}function Vt(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(k(n)?Ct:ft)(i,o,r,n),l={...Pt(i,"string"==typeof n,t),...a,ref:s},{children:u}=i,c=e.useMemo(()=>U(u)?u.get():u,[u]);return e.createElement(n,{...l,children:c})}}function Mt(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}const Dt=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Rt(t,e,n,i){Mt(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(Dt.has(n)?n:d(n),e.attrs[n])}function Lt(t,e){const{style:n}=t,i={};for(const s in n)(U(n[s])||e.style&&U(e.style[s])||I(s,t))&&(i[s]=n[s]);return i}function kt(t,e){const n=Lt(t,e);for(const i in t)if(U(t[i])||U(e[i])){n[-1!==F.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]}return n}function Bt(t,e,n,i={},s={}){return"function"==typeof e&&(e=e(void 0!==n?n:t.custom,i,s)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==n?n:t.custom,i,s)),e}function jt(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const Ft=t=>Array.isArray(t);function Ot(t){const e=U(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const It=t=>(n,i)=>{const s=e.useContext(a),o=e.useContext(l),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},i,s,o){const r={latestValues:Ut(i,s,o,t),renderState:e()};return n&&(r.mount=t=>n(i,t,r)),r}(t,n,s,o);return i?r():jt(r)};function Ut(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=Ot(o[t]);let{initial:r,animate:a}=t;const l=x(t),u=P(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!g(h)){(Array.isArray(h)?h:[h]).forEach(e=>{const n=Bt(t,e);if(!n)return;const{transitionEnd:i,transition:o,...r}=n;for(const t in r){let e=r[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const t in i)s[t]=i[t]})}return s}const Wt=t=>t;class Nt{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}const zt=["prepare","read","update","preRender","render","postRender"];const{schedule:Ht,cancel:$t,state:Yt,steps:Xt}=function(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=zt.reduce((t,e)=>(t[e]=function(t){let e=new Nt,n=new Nt,i=0,s=!1,o=!1;const r=new WeakSet,a={schedule:(t,o=!1,a=!1)=>{const l=a&&s,u=l?e:n;return o&&r.add(t),u.add(t)&&l&&s&&(i=e.order.length),t},cancel:t=>{n.remove(t),r.delete(t)},process:l=>{if(s)o=!0;else{if(s=!0,[e,n]=[n,e],n.clear(),i=e.order.length,i)for(let n=0;n<i;n++){const i=e.order[n];i(l),r.has(i)&&(a.schedule(i),t())}s=!1,o&&(o=!1,a.process(l))}}};return a}(()=>n=!0),t),{}),r=t=>o[t].process(s),a=()=>{const o=performance.now();n=!1,s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1),s.timestamp=o,s.isProcessing=!0,zt.forEach(r),s.isProcessing=!1,n&&e&&(i=!1,t(a))};return{schedule:zt.reduce((e,r)=>{const l=o[r];return e[r]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(a)),l.schedule(e,o,r)),e},{}),cancel:t=>zt.forEach(e=>o[e].cancel(t)),state:s,steps:o}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:Wt,!0),Gt={useVisualState:It({scrapeMotionValuesFromProps:kt,createRenderState:Tt,onMount:(t,e,{renderState:n,latestValues:i})=>{Ht.read(()=>{try{n.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){n.dimensions={x:0,y:0,width:0,height:0}}}),Ht.render(()=>{St(n,i,{enableHardwareAcceleration:!1},At(e.tagName),t.transformTemplate),Rt(e,n)})}})},qt={useVisualState:It({scrapeMotionValuesFromProps:Lt,createRenderState:dt})};function Zt(t,{forwardMotionProps:e=!1},n,i){return{...k(t)?Gt:qt,preloadedFeatures:n,useRender:Vt(e),createVisualElement:i,Component:t}}function Kt(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const _t=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function Jt(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}const Qt=t=>e=>_t(e)&&t(e,Jt(e));function te(t,e,n,i){return Kt(t,e,Qt(n),i)}const ee=(t,e)=>n=>e(t(n)),ne=(...t)=>t.reduce(ee);function ie(t){let e=null;return()=>{const n=()=>{e=null};return null===e&&(e=t,n)}}const se=ie("dragHorizontal"),oe=ie("dragVertical");function re(t){let e=!1;if("y"===t)e=oe();else if("x"===t)e=se();else{const t=se(),n=oe();t&&n?e=()=>{t(),n()}:(t&&t(),n&&n())}return e}function ae(){const t=re(!0);return!t||(t(),!1)}class le{constructor(t){this.isMounted=!1,this.node=t}update(){}}function ue(t,e){const n="pointer"+(e?"enter":"leave"),i="onHover"+(e?"Start":"End");return te(t.current,n,(n,s)=>{if("touch"===n.pointerType||ae())return;const o=t.getProps();t.animationState&&o.whileHover&&t.animationState.setActive("whileHover",e),o[i]&&Ht.update(()=>o[i](n,s))},{passive:!t.getProps()[i]})}const ce=(t,e)=>!!e&&(t===e||ce(t,e.parentElement));function he(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,Jt(n))}const de=new WeakMap,pe=new WeakMap,me=t=>{const e=de.get(t.target);e&&e(t)},fe=t=>{t.forEach(me)};function ge(t,e,n){const i=function({root:t,...e}){const n=t||document;pe.has(n)||pe.set(n,{});const i=pe.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(fe,{root:t,...e})),i[s]}(e);return de.set(t,n),i.observe(t),()=>{de.delete(t),i.unobserve(t)}}const ye={some:0,all:1};const ve={inView:{Feature:class extends le{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:ye[i]};return ge(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends le{constructor(){super(...arguments),this.removeStartListeners=Wt,this.removeEndListeners=Wt,this.removeAccessibleListeners=Wt,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),i=te(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:i,globalTapTarget:s}=this.node.getProps();Ht.update(()=>{s||ce(this.node.current,t.target)?n&&n(t,e):i&&i(t,e)})},{passive:!(n.onTap||n.onPointerUp)}),s=te(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=ne(i,s),this.startPress(t,e)},this.startAccessiblePress=()=>{const t=Kt(this.node.current,"keydown",t=>{if("Enter"!==t.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=Kt(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&he("up",(t,e)=>{const{onTap:n}=this.node.getProps();n&&Ht.update(()=>n(t,e))})}),he("down",(t,e)=>{this.startPress(t,e)})}),e=Kt(this.node.current,"blur",()=>{this.isPressing&&he("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=ne(t,e)}}startPress(t,e){this.isPressing=!0;const{onTapStart:n,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&Ht.update(()=>n(t,e))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!ae()}cancelPress(t,e){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&Ht.update(()=>n(t,e))}mount(){const t=this.node.getProps(),e=te(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),n=Kt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=ne(e,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends le{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ne(Kt(this.node.current,"focus",()=>this.onFocus()),Kt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends le{mount(){this.unmount=ne(ue(this.node,!0),ue(this.node,!1))}unmount(){}}}};function xe(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function Pe(t,e,n){const i=t.getProps();return Bt(i,e,void 0!==n?n:i.custom,function(t){const e={};return t.values.forEach((t,n)=>e[n]=t.get()),e}(t),function(t){const e={};return t.values.forEach((t,n)=>e[n]=t.getVelocity()),e}(t))}let we=Wt,Ee=Wt;const be=t=>1e3*t,Se=t=>t/1e3,Te={current:!1},Ae=t=>Array.isArray(t)&&"number"==typeof t[0];function Ce(t){return Boolean(!t||"string"==typeof t&&Me[t]||Ae(t)||Array.isArray(t)&&t.every(Ce))}const Ve=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Me={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ve([0,.65,.55,1]),circOut:Ve([.55,0,1,.45]),backIn:Ve([.31,.01,.66,-.59]),backOut:Ve([.33,1.53,.69,.99])};function De(t){if(t)return Ae(t)?Ve(t):Array.isArray(t)?t.map(De):Me[t]}function Re(t,e,n,{delay:i=0,duration:s,repeat:o=0,repeatType:r="loop",ease:a,times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=De(a);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}const Le=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function ke(t,e,n,i){if(t===e&&n===i)return Wt;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=Le(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:Le(s(t),e,i)}const Be=ke(.42,0,1,1),je=ke(0,0,.58,1),Fe=ke(.42,0,.58,1),Oe=t=>Array.isArray(t)&&"number"!=typeof t[0],Ie=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ue=t=>e=>1-t(1-e),We=t=>1-Math.sin(Math.acos(t)),Ne=Ue(We),ze=Ie(We),He=ke(.33,1.53,.69,.99),$e=Ue(He),Ye=Ie($e),Xe=t=>(t*=2)<1?.5*$e(t):.5*(2-Math.pow(2,-10*(t-1))),Ge={linear:Wt,easeIn:Be,easeInOut:Fe,easeOut:je,circIn:We,circInOut:ze,circOut:Ne,backIn:$e,backInOut:Ye,backOut:He,anticipate:Xe},qe=t=>{if(Array.isArray(t)){Ee(4===t.length);const[e,n,i,s]=t;return ke(e,n,i,s)}return"string"==typeof t?(Ee(void 0!==Ge[t]),Ge[t]):t},Ze=(t,e)=>n=>Boolean(et(n)&&tt.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),Ke=(t,e,n)=>i=>{if(!et(i))return i;const[s,o,r,a]=i.match(J);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},_e={...q,transform:t=>Math.round((t=>G(0,255,t))(t))},Je={test:Ze("rgb","red"),parse:Ke("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+_e.transform(t)+", "+_e.transform(e)+", "+_e.transform(n)+", "+_(Z.transform(i))+")"};const Qe={test:Ze("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Je.transform},tn={test:Ze("hsl","hue"),parse:Ke("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+st.transform(_(e))+", "+st.transform(_(n))+", "+_(Z.transform(i))+")"},en={test:t=>Je.test(t)||Qe.test(t)||tn.test(t),parse:t=>Je.test(t)?Je.parse(t):tn.test(t)?tn.parse(t):Qe.parse(t),transform:t=>et(t)?t:t.hasOwnProperty("red")?Je.transform(t):tn.transform(t)},nn=(t,e,n)=>-n*t+n*e+t;function sn(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}const on=(t,e,n)=>{const i=t*t;return Math.sqrt(Math.max(0,n*(e*e-i)+i))},rn=[Qe,Je,tn];function an(t){const e=(n=t,rn.find(t=>t.test(n)));var n;Ee(Boolean(e));let i=e.parse(t);return e===tn&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=sn(a,i,t+1/3),o=sn(a,i,t),r=sn(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const ln=(t,e)=>{const n=an(t),i=an(e),s={...n};return t=>(s.red=on(n.red,i.red,t),s.green=on(n.green,i.green,t),s.blue=on(n.blue,i.blue,t),s.alpha=nn(n.alpha,i.alpha,t),Je.transform(s))};const un={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:Wt},cn={regex:Q,countKey:"Colors",token:"${c}",parse:en.parse},hn={regex:J,countKey:"Numbers",token:"${n}",parse:q.parse};function dn(t,{regex:e,countKey:n,token:i,parse:s}){const o=t.tokenised.match(e);o&&(t["num"+n]=o.length,t.tokenised=t.tokenised.replace(e,i),t.values.push(...o.map(s)))}function pn(t){const e=t.toString(),n={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&dn(n,un),dn(n,cn),dn(n,hn),n}function mn(t){return pn(t).values}function fn(t){const{values:e,numColors:n,numVars:i,tokenised:s}=pn(t),o=e.length;return t=>{let e=s;for(let s=0;s<o;s++)e=s<i?e.replace(un.token,t[s]):s<i+n?e.replace(cn.token,en.transform(t[s])):e.replace(hn.token,_(t[s]));return e}}const gn=t=>"number"==typeof t?0:t;const yn={test:function(t){var e,n;return isNaN(t)&&et(t)&&((null===(e=t.match(J))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(Q))||void 0===n?void 0:n.length)||0)>0},parse:mn,createTransformer:fn,getAnimatableNone:function(t){const e=mn(t);return fn(t)(e.map(gn))}},vn=(t,e)=>n=>""+(n>0?e:t);function xn(t,e){return"number"==typeof t?n=>nn(t,e,n):en.test(t)?ln(t,e):t.startsWith("var(")?vn(t,e):En(t,e)}const Pn=(t,e)=>{const n=[...t],i=n.length,s=t.map((t,n)=>xn(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}},wn=(t,e)=>{const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=xn(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}},En=(t,e)=>{const n=yn.createTransformer(e),i=pn(t),s=pn(e);return i.numVars===s.numVars&&i.numColors===s.numColors&&i.numNumbers>=s.numNumbers?ne(Pn(i.values,s.values),n):(we(!0),vn(t,e))},bn=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},Sn=(t,e)=>n=>nn(t,e,n);function Tn(t,e,n){const i=[],s=n||("number"==typeof(o=t[0])?Sn:"string"==typeof o?en.test(o)?ln:En:Array.isArray(o)?Pn:"object"==typeof o?wn:Sn);var o;const r=t.length-1;for(let n=0;n<r;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||Wt:e;o=ne(t,o)}i.push(o)}return i}function An(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(Ee(o===e.length),1===o)return()=>e[0];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const r=Tn(e,i,s),a=r.length,l=e=>{let n=0;if(a>1)for(;n<t.length-2&&!(e<t[n+1]);n++);const i=bn(t[n],t[n+1],e);return r[n](i)};return n?e=>l(G(t[0],t[o-1],e)):l}function Cn(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=bn(0,e,i);t.push(nn(n,1,s))}}function Vn(t){const e=[0];return Cn(e,t.length-1),e}function Mn({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=Oe(i)?i.map(qe):qe(i),o={done:!1,value:e[0]},r=An(function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:Vn(e),t),e,{ease:Array.isArray(s)?s:(a=e,l=s,a.map(()=>l||Fe).splice(0,a.length-1))});var a,l;return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}function Dn(t,e){return e?t*(1e3/e):0}function Rn(t,e,n){const i=Math.max(e-5,0);return Dn(n-t(i),e-i)}function Ln({duration:t=800,bounce:e=.25,velocity:n=0,mass:i=1}){let s,o;we(t<=be(10));let r=1-e;r=G(.05,1,r),t=G(.01,10,Se(t)),r<1?(s=e=>{const i=e*r,s=i*t;return.001-(i-n)/kn(e,r)*Math.exp(-s)},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=kn(Math.pow(e,2),r);return(.001-s(e)>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<12;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=be(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}function kn(t,e){return t*Math.sqrt(1-e*e)}const Bn=["duration","bounce"],jn=["stiffness","damping","mass"];function Fn(t,e){return e.some(e=>void 0!==t[e])}function On({keyframes:t,restDelta:e,restSpeed:n,...i}){const s=t[0],o=t[t.length-1],r={done:!1,value:s},{stiffness:a,damping:l,mass:u,duration:c,velocity:h,isResolvedFromDuration:d}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!Fn(t,jn)&&Fn(t,Bn)){const n=Ln(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}({...i,velocity:-Se(i.velocity||0)}),p=h||0,m=l/(2*Math.sqrt(a*u)),f=o-s,g=Se(Math.sqrt(a/u)),y=Math.abs(f)<5;let v;if(n||(n=y?.01:2),e||(e=y?.005:.5),m<1){const t=kn(g,m);v=e=>{const n=Math.exp(-m*g*e);return o-n*((p+m*g*f)/t*Math.sin(t*e)+f*Math.cos(t*e))}}else if(1===m)v=t=>o-Math.exp(-g*t)*(f+(p+g*f)*t);else{const t=g*Math.sqrt(m*m-1);v=e=>{const n=Math.exp(-m*g*e),i=Math.min(t*e,300);return o-n*((p+m*g*f)*Math.sinh(i)+t*f*Math.cosh(i))/t}}return{calculatedDuration:d&&c||null,next:t=>{const i=v(t);if(d)r.done=t>=c;else{let s=p;0!==t&&(s=m<1?Rn(v,t,i):0);const a=Math.abs(s)<=n,l=Math.abs(o-i)<=e;r.done=a&&l}return r.value=r.done?o:i,r}}}function In({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let P,w;const E=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(P=t,w=On({keyframes:[d.value,p(d.value)],velocity:Rn(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==P||(e=!0,x(t),E(t)),void 0!==P&&t>P?w.next(t-P):(!e&&x(t),d)}}}const Un=t=>{const e=({timestamp:e})=>t(e);return{start:()=>Ht.update(e,!0),stop:()=>$t(e),now:()=>Yt.isProcessing?Yt.timestamp:performance.now()}};function Wn(t){let e=0;let n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}const Nn={decay:In,inertia:In,tween:Mn,keyframes:Mn,spring:On};function zn({autoplay:t=!0,delay:e=0,driver:n=Un,keyframes:i,type:s="keyframes",repeat:o=0,repeatDelay:r=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:h,...d}){let p,m,f=1,g=!1;const y=()=>{m=new Promise(t=>{p=t})};let v;y();const x=Nn[s]||Mn;let P;x!==Mn&&"number"!=typeof i[0]&&(P=An([0,100],i,{clamp:!1}),i=[0,100]);const w=x({...d,keyframes:i});let E;"mirror"===a&&(E=x({...d,keyframes:[...i].reverse(),velocity:-(d.velocity||0)}));let b="idle",S=null,T=null,A=null;null===w.calculatedDuration&&o&&(w.calculatedDuration=Wn(w));const{calculatedDuration:C}=w;let V=1/0,M=1/0;null!==C&&(V=C+r,M=V*(o+1)-r);let D=0;const R=t=>{if(null===T)return;f>0&&(T=Math.min(T,t)),f<0&&(T=Math.min(t-M/f,T)),D=null!==S?S:Math.round(t-T)*f;const n=D-e*(f>=0?1:-1),s=f>=0?n<0:n>M;D=Math.max(n,0),"finished"===b&&null===S&&(D=M);let l=D,u=w;if(o){const t=Math.min(D,M)/V;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,o+1);Boolean(e%2)&&("reverse"===a?(n=1-n,r&&(n-=r/V)):"mirror"===a&&(u=E)),l=G(0,1,n)*V}const c=s?{done:!1,value:i[0]}:u.next(l);P&&(c.value=P(c.value));let{done:d}=c;s||null===C||(d=f>=0?D>=M:D<=0);const p=null===S&&("finished"===b||"running"===b&&d);return h&&h(c.value),p&&B(),c},L=()=>{v&&v.stop(),v=void 0},k=()=>{b="idle",L(),p(),y(),T=A=null},B=()=>{b="finished",c&&c(),L(),p()},j=()=>{if(g)return;v||(v=n(R));const t=v.now();l&&l(),null!==S?T=t-S:T&&"finished"!==b||(T=t),"finished"===b&&y(),A=T,S=null,b="running",v.start()};t&&j();const F={then:(t,e)=>m.then(t,e),get time(){return Se(D)},set time(t){t=be(t),D=t,null===S&&v&&0!==f?T=v.now()-t/f:S=t},get duration(){const t=null===w.calculatedDuration?Wn(w):w.calculatedDuration;return Se(t)},get speed(){return f},set speed(t){t!==f&&v&&(f=t,F.time=Se(D))},get state(){return b},play:j,pause:()=>{b="paused",S=D},stop:()=>{g=!0,"idle"!==b&&(b="idle",u&&u(),k())},cancel:()=>{null!==A&&R(A),k()},complete:()=>{b="finished"},sample:t=>(T=0,R(t))};return F}function Hn(t){let e;return()=>(void 0===e&&(e=t()),e)}const $n=Hn(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Yn=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function Xn(t,e,{onUpdate:n,onComplete:i,...s}){if(!($n()&&Yn.has(e)&&!s.repeatDelay&&"mirror"!==s.repeatType&&0!==s.damping&&"inertia"!==s.type))return!1;let o,r,a=!1,l=!1;const u=()=>{r=new Promise(t=>{o=t})};u();let{keyframes:c,duration:h=300,ease:d,times:p}=s;if(((t,e)=>"spring"===e.type||"backgroundColor"===t||!Ce(e.ease))(e,s)){const t=zn({...s,repeat:0,delay:0});let e={done:!1,value:c[0]};const n=[];let i=0;for(;!e.done&&i<2e4;)e=t.sample(i),n.push(e.value),i+=10;p=void 0,c=n,h=i-10,d="linear"}const m=Re(t.owner.current,e,c,{...s,duration:h,ease:d,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,Ht.update(f),o(),u()};m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:n="loop"}){return t[e&&"loop"!==n&&e%2==1?0:t.length-1]}(c,s)),i&&i(),g())};return{then:(t,e)=>r.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,Wt),get time(){return Se(m.currentTime||0)},set time(t){m.currentTime=be(t)},get speed(){return m.playbackRate},set speed(t){m.playbackRate=t},get duration(){return Se(h)},play:()=>{a||(m.play(),$t(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;const{currentTime:e}=m;if(e){const n=zn({...s,autoplay:!1});t.setWithVelocity(n.sample(e-10).value,n.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}const Gn={type:"spring",stiffness:500,damping:25,restSpeed:10},qn={type:"keyframes",duration:.8},Zn={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Kn=(t,{keyframes:e})=>e.length>2?qn:O.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Gn:Zn,_n=(t,e)=>"zIndex"!==t&&(!("number"!=typeof e&&!Array.isArray(e))||!("string"!=typeof e||!yn.test(e)&&"0"!==e||e.startsWith("url("))),Jn=new Set(["brightness","contrast","saturate","opacity"]);function Qn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(J)||[];if(!i)return t;const s=n.replace(i,"");let o=Jn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const ti=/([a-z-]*)\(.*?\)/g,ei={...yn,getAnimatableNone:t=>{const e=t.match(ti);return e?e.map(Qn).join(" "):t}},ni={...ct,color:en,backgroundColor:en,outlineColor:en,fill:en,stroke:en,borderColor:en,borderTopColor:en,borderRightColor:en,borderBottomColor:en,borderLeftColor:en,filter:ei,WebkitFilter:ei},ii=t=>ni[t];function si(t,e){let n=ii(t);return n!==ei&&(n=yn),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const oi=t=>/^0[^.\s]+$/.test(t);function ri(t){return"number"==typeof t?0===t:null!==t?"none"===t||"0"===t||oi(t):void 0}function ai(t,e){return t[e]||t.default||t}const li={skipAnimations:!1},ui=(t,e,n,i={})=>s=>{const o=ai(i,t)||{},r=o.delay||i.delay||0;let{elapsed:a=0}=i;a-=be(r);const l=function(t,e,n,i){const s=_n(e,n);let o;o=Array.isArray(n)?[...n]:[null,n];const r=void 0!==i.from?i.from:t.get();let a=void 0;const l=[];for(let t=0;t<o.length;t++)null===o[t]&&(o[t]=0===t?r:o[t-1]),ri(o[t])&&l.push(t),"string"==typeof o[t]&&"none"!==o[t]&&"0"!==o[t]&&(a=o[t]);if(s&&l.length&&a)for(let t=0;t<l.length;t++){o[l[t]]=si(e,a)}return o}(e,t,n,o),u=l[0],c=l[l.length-1],h=_n(t,u),d=_n(t,c);we(h===d);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{s(),o.onComplete&&o.onComplete()}};if(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)||(p={...p,...Kn(t,p)}),p.duration&&(p.duration=be(p.duration)),p.repeatDelay&&(p.repeatDelay=be(p.repeatDelay)),!h||!d||Te.current||!1===o.type||li.skipAnimations)return function({keyframes:t,delay:e,onUpdate:n,onComplete:i}){const s=()=>(n&&n(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:Wt,pause:Wt,stop:Wt,then:t=>(t(),Promise.resolve()),cancel:Wt,complete:Wt});return e?zn({keyframes:[0,1],duration:0,delay:e,onComplete:s}):s()}(Te.current?{...p,delay:0}:p);if(!i.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){const n=Xn(e,t,p);if(n)return n}return zn(p)};function ci(t){return Boolean(U(t)&&t.add)}const hi=t=>/^\-?\d*\.?\d+$/.test(t);function di(t,e){-1===t.indexOf(e)&&t.push(e)}function pi(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class mi{constructor(){this.subscriptions=[]}add(t){return di(this.subscriptions,t),()=>pi(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const fi={current:void 0};class gi{constructor(t,e={}){var n;this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;const{delta:n,timestamp:i}=Yt;this.lastUpdated!==i&&(this.timeDelta=n,this.lastUpdated=i,Ht.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>Ht.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=(n=this.current,!isNaN(parseFloat(n))),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new mi);const n=this.events[t].add(e);return"change"===t?()=>{n(),Ht.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=t,this.timeDelta=n}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return fi.current&&fi.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Dn(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function yi(t,e){return new gi(t,e)}const vi=t=>e=>e.test(t),xi=[q,ot,st,it,at,rt,{test:t=>"auto"===t,parse:t=>t}],Pi=t=>xi.find(vi(t)),wi=[...xi,en,yn];function Ei(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,yi(n))}function bi(t,e){const n=Pe(t,e);let{transitionEnd:i={},transition:s={},...o}=n?t.makeTargetAnimatable(n,!1):{};o={...o,...i};for(const e in o){Ei(t,e,(r=o[e],Ft(r)?r[r.length-1]||0:r))}var r}function Si(t,e){[...e].reverse().forEach(n=>{const i=t.getVariant(n);i&&bi(t,i),t.variantChildren&&t.variantChildren.forEach(t=>{Si(t,e)})})}function Ti(t,e,n){var i,s;const o=Object.keys(e).filter(e=>!t.hasValue(e)),r=o.length;var a;if(r)for(let l=0;l<r;l++){const r=o[l],u=e[r];let c=null;Array.isArray(u)&&(c=u[0]),null===c&&(c=null!==(s=null!==(i=n[r])&&void 0!==i?i:t.readValue(r))&&void 0!==s?s:e[r]),null!=c&&("string"==typeof c&&(hi(c)||oi(c))?c=parseFloat(c):(a=c,!wi.find(vi(a))&&yn.test(u)&&(c=si(r,u))),t.addValue(r,yi(c,{owner:t})),void 0===n[r]&&(n[r]=c),null!==c&&t.setBaseTarget(r,c))}}function Ai(t,e){if(!e)return;return(e[t]||e.default||e).from}function Ci(t,e,n){const i={};for(const s in t){const t=Ai(s,e);if(void 0!==t)i[s]=t;else{const t=n.getValue(s);t&&(i[s]=t.get())}}return i}function Vi({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function Mi(t,e){const n=t.get();if(!Array.isArray(e))return n!==e;for(let t=0;t<e.length;t++)if(e[t]!==n)return!0}function Di(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=t.makeTargetAnimatable(e);const l=t.getValue("willChange");i&&(o=i);const u=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const e in a){const i=t.getValue(e),s=a[e];if(!i||void 0===s||c&&Vi(c,e))continue;const r={delay:n,elapsed:0,...ai(o||{},e)};if(window.HandoffAppearAnimations){const n=t.getProps()[p];if(n){const t=window.HandoffAppearAnimations(n,e,i,Ht);null!==t&&(r.elapsed=t,r.isHandoff=!0)}}let h=!r.isHandoff&&!Mi(i,s);if("spring"===r.type&&(i.getVelocity()||r.velocity)&&(h=!1),i.animation&&(h=!1),h)continue;i.start(ui(e,i,s,t.shouldReduceMotion&&O.has(e)?{type:!1}:r));const d=i.animation;ci(l)&&(l.add(e),d.then(()=>l.remove(e))),u.push(d)}return r&&Promise.all(u).then(()=>{r&&bi(t,r)}),u}function Ri(t,e,n={}){const i=Pe(t,e,n.custom);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(Di(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(Li).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(Ri(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,o+i,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(n.delay)])}function Li(t,e){return t.sortNodePosition(e)}function ki(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>Ri(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=Ri(t,e,n);else{const s="function"==typeof e?Pe(t,e,n.custom):e;i=Promise.all(Di(t,s,n))}return i.then(()=>t.notify("AnimationComplete",e))}const Bi=[...y].reverse(),ji=y.length;function Fi(t){let e=function(t){return e=>Promise.all(e.map(({animation:e,options:n})=>ki(t,e,n)))}(t);const n={animate:Ii(!0),whileInView:Ii(),whileHover:Ii(),whileTap:Ii(),whileDrag:Ii(),whileFocus:Ii(),exit:Ii()};let i=!0;const s=(e,n)=>{const i=Pe(t,n);if(i){const{transition:t,transitionEnd:n,...s}=i;e={...e,...s,...n}}return e};function o(o,r){const a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],c=new Set;let h={},d=1/0;for(let e=0;e<ji;e++){const p=Bi[e],m=n[p],y=void 0!==a[p]?a[p]:l[p],v=f(y),x=p===r?m.isActive:null;!1===x&&(d=e);let P=y===l[p]&&y!==a[p]&&v;if(P&&i&&t.manuallyAnimateOnMount&&(P=!1),m.protectedKeys={...h},!m.isActive&&null===x||!y&&!m.prevProp||g(y)||"boolean"==typeof y)continue;let w=Oi(m.prevProp,y)||p===r&&m.isActive&&!P&&v||e>d&&v,E=!1;const b=Array.isArray(y)?y:[y];let S=b.reduce(s,{});!1===x&&(S={});const{prevResolvedValues:T={}}=m,A={...T,...S},C=t=>{w=!0,c.has(t)&&(E=!0,c.delete(t)),m.needsAnimating[t]=!0};for(const t in A){const e=S[t],n=T[t];if(h.hasOwnProperty(t))continue;let i=!1;i=Ft(e)&&Ft(n)?!xe(e,n):e!==n,i?void 0!==e?C(t):c.add(t):void 0!==e&&c.has(t)?C(t):m.protectedKeys[t]=!0}m.prevProp=y,m.prevResolvedValues=S,m.isActive&&(h={...h,...S}),i&&t.blockInitialAnimation&&(w=!1),!w||P&&!E||u.push(...b.map(t=>({animation:t,options:{type:p,...o}})))}if(c.size){const e={};c.forEach(n=>{const i=t.getBaseTarget(n);void 0!==i&&(e[n]=i)}),u.push({animation:e})}let p=Boolean(u.length);return!i||!1!==a.initial&&a.initial!==a.animate||t.manuallyAnimateOnMount||(p=!1),i=!1,p?e(u):Promise.resolve()}return{animateChanges:o,setActive:function(e,i,s){var r;if(n[e].isActive===i)return Promise.resolve();null===(r=t.variantChildren)||void 0===r||r.forEach(t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)}),n[e].isActive=i;const a=o(s,e);for(const t in n)n[t].protectedKeys={};return a},setAnimateFunction:function(n){e=n(t)},getState:()=>n}}function Oi(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!xe(e,t)}function Ii(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let Ui=0;const Wi={animation:{Feature:class extends le{constructor(t){super(t),t.animationState||(t.animationState=Fi(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),g(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends le{constructor(){super(...arguments),this.id=Ui++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e,custom:n}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t,{custom:null!=n?n:this.node.getProps().custom});e&&!t&&s.then(()=>e(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}}},Ni=(t,e)=>Math.abs(t-e);function zi(t,e){const n=Ni(t.x,e.x),i=Ni(t.y,e.y);return Math.sqrt(n**2+i**2)}class Hi{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Xi(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=zi(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=Yt;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=$i(e,this.transformPagePoint),Ht.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Xi("pointercancel"===t.type?this.lastMoveEventInfo:$i(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!_t(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=$i(Jt(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=Yt;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Xi(o,this.history)),this.removeListeners=ne(te(this.contextWindow,"pointermove",this.handlePointerMove),te(this.contextWindow,"pointerup",this.handlePointerUp),te(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),$t(this.updatePoint)}}function $i(t,e){return e?{point:e(t.point)}:t}function Yi(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Xi({point:t},e){return{point:t,delta:Yi(t,qi(e)),offset:Yi(t,Gi(e)),velocity:Zi(e,.1)}}function Gi(t){return t[0]}function qi(t){return t[t.length-1]}function Zi(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=qi(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>be(e)));)n--;if(!i)return{x:0,y:0};const o=Se(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Ki(t){return t.max-t.min}function _i(t,e=0,n=.01){return Math.abs(t-e)<=n}function Ji(t,e,n,i=.5){t.origin=i,t.originPoint=nn(e.min,e.max,t.origin),t.scale=Ki(n)/Ki(e),(_i(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=nn(n.min,n.max,t.origin)-t.originPoint,(_i(t.translate)||isNaN(t.translate))&&(t.translate=0)}function Qi(t,e,n,i){Ji(t.x,e.x,n.x,i?i.originX:void 0),Ji(t.y,e.y,n.y,i?i.originY:void 0)}function ts(t,e,n){t.min=n.min+e.min,t.max=t.min+Ki(e)}function es(t,e,n){t.min=e.min-n.min,t.max=t.min+Ki(e)}function ns(t,e,n){es(t.x,e.x,n.x),es(t.y,e.y,n.y)}function is(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function ss(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const os=.35;function rs(t,e,n){return{min:as(t,e),max:as(t,n)}}function as(t,e){return"number"==typeof t?t:t[e]||0}const ls=()=>({x:{min:0,max:0},y:{min:0,max:0}});function us(t){return[t("x"),t("y")]}function cs({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function hs(t){return void 0===t||1===t}function ds({scale:t,scaleX:e,scaleY:n}){return!hs(t)||!hs(e)||!hs(n)}function ps(t){return ds(t)||ms(t)||t.z||t.rotate||t.rotateX||t.rotateY}function ms(t){return fs(t.x)||fs(t.y)}function fs(t){return t&&"0%"!==t}function gs(t,e,n){return n+e*(t-n)}function ys(t,e,n,i,s){return void 0!==s&&(t=gs(t,s,i)),gs(t,n,i)+e}function vs(t,e=0,n=1,i,s){t.min=ys(t.min,e,n,i,s),t.max=ys(t.max,e,n,i,s)}function xs(t,{x:e,y:n}){vs(t.x,e.translate,e.scale,e.originPoint),vs(t.y,n.translate,n.scale,n.originPoint)}function Ps(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function ws(t,e){t.min=t.min+e,t.max=t.max+e}function Es(t,e,[n,i,s]){const o=void 0!==e[s]?e[s]:.5,r=nn(t.min,t.max,o);vs(t,e[n],e[i],r,e.scale)}const bs=["x","scaleX","originX"],Ss=["y","scaleY","originY"];function Ts(t,e){Es(t.x,e,bs),Es(t.y,e,Ss)}function As(t,e){return cs(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const Cs=({current:t})=>t?t.ownerDocument.defaultView:null,Vs=new WeakMap;class Ms{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Hi(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Jt(t,"page").point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=re(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),us(t=>{let e=this.getAxisMotionValue(t).get()||0;if(st.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Ki(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&Ht.update(()=>s(t,e),!1,!0);const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>us(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:Cs(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&Ht.update(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Ds(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?nn(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?nn(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&m(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:is(t.x,n,s),y:is(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=os){return!1===t?t=0:!0===t&&(t=os),{x:rs(t,"left","right"),y:rs(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&us(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!m(t))return!1;const n=t.current;Ee(null!==n);const{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=As(t,n),{scroll:s}=e;return s&&(ws(i.x,s.offset.x),ws(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:ss(t.x,e.x),y:ss(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=cs(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=us(r=>{if(!Ds(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return n.start(ui(t,n,0,e))}stopAnimation(){us(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){us(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e="_drag"+t.toUpperCase(),n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){us(e=>{const{drag:n}=this.getProps();if(!Ds(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-nn(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!m(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};us(t=>{const e=this.getAxisMotionValue(t);if(e){const n=e.get();i[t]=function(t,e){let n=.5;const i=Ki(t),s=Ki(e);return s>i?n=bn(e.min,e.max-i,t.min):i>s&&(n=bn(t.min,t.max-s,e.min)),G(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),us(e=>{if(!Ds(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(nn(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;Vs.set(this.visualElement,this);const t=te(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();m(t)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),e();const s=Kt(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(us(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=os,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Ds(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const Rs=t=>(e,n)=>{t&&Ht.update(()=>t(e,n))};const Ls=["TopLeft","TopRight","BottomLeft","BottomRight"],ks=Ls.length,Bs=t=>"string"==typeof t?parseFloat(t):t,js=t=>"number"==typeof t||ot.test(t);function Fs(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Os=Us(0,.5,Ne),Is=Us(.5,.95,Wt);function Us(t,e,n){return i=>i<t?0:i>e?1:n(bn(t,e,i))}function Ws(t,e){t.min=e.min,t.max=e.max}function Ns(t,e){Ws(t.x,e.x),Ws(t.y,e.y)}function zs(t,e,n,i,s){return t=gs(t-=e,1/n,i),void 0!==s&&(t=gs(t,1/s,i)),t}function Hs(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){if(st.test(e)){e=parseFloat(e);e=nn(r.min,r.max,e/100)-r.min}if("number"!=typeof e)return;let a=nn(o.min,o.max,i);t===o&&(a-=e),t.min=zs(t.min,e,n,a,s),t.max=zs(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const $s=["x","scaleX","originX"],Ys=["y","scaleY","originY"];function Xs(t,e,n,i){Hs(t.x,e,$s,n?n.x:void 0,i?i.x:void 0),Hs(t.y,e,Ys,n?n.y:void 0,i?i.y:void 0)}function Gs(t){return 0===t.translate&&1===t.scale}function qs(t){return Gs(t.x)&&Gs(t.y)}function Zs(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Ks(t){return Ki(t.x)/Ki(t.y)}class _s{constructor(){this.members=[]}add(t){di(this.members,t),t.scheduleRender()}remove(t){if(pi(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Js(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y;if((s||o)&&(i=`translate3d(${s}px, ${o}px, 0) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:t,rotateX:e,rotateY:s}=n;t&&(i+=`rotate(${t}deg) `),e&&(i+=`rotateX(${e}deg) `),s&&(i+=`rotateY(${s}deg) `)}const r=t.x.scale*e.x,a=t.y.scale*e.y;return 1===r&&1===a||(i+=`scale(${r}, ${a})`),i||"none"}const Qs=(t,e)=>t.depth-e.depth;class to{constructor(){this.children=[],this.isDirty=!1}add(t){di(this.children,t),this.isDirty=!0}remove(t){pi(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Qs),this.isDirty=!1,this.children.forEach(t)}}const eo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function no(t,e){const n=performance.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&($t(i),t(o-e))};return Ht.read(i,!0),()=>$t(i)}function io(t,e,n){var i;if("string"==typeof t){let s=document;e&&(Ee(Boolean(e.current)),s=e.current),n?(null!==(i=n[t])&&void 0!==i||(n[t]=s.querySelectorAll(t)),t=n[t]):t=s.querySelectorAll(t)}else t instanceof Element&&(t=[t]);return Array.from(t||[])}const so=new WeakMap;function oo(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return Ht.update(i,!0),()=>$t(i)}const ro=Hn(()=>void 0!==window.ScrollTimeline);class ao{constructor(t){this.animations=t.filter(Boolean)}then(t,e){return Promise.all(this.animations).then(t).catch(e)}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>{if(!ro()||!e.attachTimeline)return e.pause(),oo(t=>{e.time=e.duration*t},t);e.attachTimeline(t)});return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}stop(){this.runAll("stop")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}function lo(t){return t instanceof SVGElement&&"svg"!==t.tagName}const uo=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function co(t,e,n=1){Ee(n<=4);const[i,s]=function(t){const e=uo.exec(t);if(!e)return[,];const[,n,i]=e;return[n,i]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return hi(t)?parseFloat(t):t}return Y(s)?co(s,e,n+1):s}const ho=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),po=t=>ho.has(t),mo=t=>t===q||t===ot,fo=(t,e)=>parseFloat(t.split(", ")[e]),go=(t,e)=>(n,{transform:i})=>{if("none"===i||!i)return 0;const s=i.match(/^matrix3d\((.+)\)$/);if(s)return fo(s[1],e);{const e=i.match(/^matrix\((.+)\)$/);return e?fo(e[1],t):0}},yo=new Set(["x","y","z"]),vo=F.filter(t=>!yo.has(t));const xo={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:go(4,13),y:go(5,14)};xo.translateX=xo.x,xo.translateY=xo.y;const Po=(t,e,n={},i={})=>{e={...e},i={...i};const s=Object.keys(e).filter(po);let o=[],r=!1;const a=[];if(s.forEach(s=>{const l=t.getValue(s);if(!t.hasValue(s))return;let u=n[s],c=Pi(u);const h=e[s];let d;if(Ft(h)){const t=h.length,e=null===h[0]?1:0;u=h[e],c=Pi(u);for(let n=e;n<t&&null!==h[n];n++)d?Ee(Pi(h[n])===d):(d=Pi(h[n]),Ee(d===c||mo(c)&&mo(d)))}else d=Pi(h);if(c!==d)if(mo(c)&&mo(d)){const t=l.get();"string"==typeof t&&l.set(parseFloat(t)),"string"==typeof h?e[s]=parseFloat(h):Array.isArray(h)&&d===ot&&(e[s]=h.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==d?void 0:d.transform)&&(0===u||0===h)?0===u?l.set(d.transform(u)):e[s]=c.transform(h):(r||(o=function(t){const e=[];return vo.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),r=!0),a.push(s),i[s]=void 0!==i[s]?i[s]:e[s],l.jump(h))}),a.length){const n=a.indexOf("height")>=0?window.pageYOffset:null,s=((t,e,n)=>{const i=e.measureViewportBox(),s=e.current,o=getComputedStyle(s),{display:r}=o,a={};"none"===r&&e.setStaticValue("display",t.display||"block"),n.forEach(t=>{a[t]=xo[t](i,o)}),e.render();const l=e.measureViewportBox();return n.forEach(n=>{const i=e.getValue(n);i&&i.jump(a[n]),t[n]=xo[n](l,o)}),t})(e,t,a);return o.length&&o.forEach(([e,n])=>{t.getValue(e).set(n)}),t.render(),u&&null!==n&&window.scrollTo({top:n}),{target:s,transitionEnd:i}}return{target:e,transitionEnd:i}};function wo(t,e,n,i){return(t=>Object.keys(t).some(po))(e)?Po(t,e,n,i):{target:e,transitionEnd:i}}const Eo=(t,e,n,i)=>{const s=function(t,{...e},n){const i=t.current;if(!(i instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(t=>{const e=t.get();if(!Y(e))return;const n=co(e,i);n&&t.set(n)});for(const t in e){const s=e[t];if(!Y(s))continue;const o=co(s,i);o&&(e[t]=o,n||(n={}),void 0===n[t]&&(n[t]=s))}return{target:e,transitionEnd:n}}(t,e,i);return wo(t,e=s.target,n,i=s.transitionEnd)},bo={current:null},So={current:!1};function To(){if(So.current=!0,u)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>bo.current=t.matches;t.addListener(e),e()}else bo.current=!1}const Ao=Object.keys(S),Co=Ao.length,Vo=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Mo=v.length;class Do{constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>Ht.render(this.render,!1,!0);const{latestValues:r,renderState:a}=s;this.latestValues=r,this.baseTarget={...r},this.initialValues=e.initial?{...r}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=x(e),this.isVariantNode=P(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(const t in u){const e=u[t];void 0!==r[t]&&U(e)&&(e.set(r[t],!1),ci(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,so.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),So.current||To(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||bo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){so.delete(this.current),this.projection&&this.projection.unmount(),$t(this.notifyUpdate),$t(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){const n=O.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&Ht.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),s()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},n,i,s){let o,r;for(let t=0;t<Co;t++){const n=Ao[t],{isEnabled:i,Feature:s,ProjectionNode:a,MeasureLayout:l}=S[n];a&&(o=a),i(e)&&(!this.features[n]&&s&&(this.features[n]=new s(this)),l&&(r=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:t,layout:n,drag:i,dragConstraints:r,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:n,alwaysMeasureLayout:Boolean(i)||r&&m(r),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,layoutScroll:a,layoutRoot:l})}return r}updateFeatures(){for(const t in this.features){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Vo.length;e++){const n=Vo[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){const{willChange:i}=e;for(const s in e){const o=e[s],r=n[s];if(U(o))t.addValue(s,o),ci(i)&&i.add(s);else if(U(r))t.addValue(s,yi(o,{owner:t})),ci(i)&&i.remove(s);else if(r!==o)if(t.hasValue(s)){const e=t.getValue(s);!e.hasAnimated&&e.set(o)}else{const e=t.getStaticValue(s);t.addValue(s,yi(void 0!==e?e:o,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}const e={};for(let t=0;t<Mo;t++){const n=v[t],i=this.props[n];(f(i)||!1===i)&&(e[n]=i)}return e}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=yi(e,{owner:this}),this.addValue(t,n)),n}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props,i="string"==typeof n||"object"==typeof n?null===(e=Bt(this.props,n))||void 0===e?void 0:e[t]:void 0;if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||U(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new mi),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Ro extends Do{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...n},{transformValues:i},s){let o=Ci(n,t||{},this);if(i&&(e&&(e=i(e)),n&&(n=i(n)),o&&(o=i(o))),s){Ti(this,n,o);const t=Eo(this,n,o,e);e=t.transitionEnd,n=t.target}return{transition:t,transitionEnd:e,...n}}}class Lo extends Ro{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(O.has(e)){const t=ii(e);return t&&t.default||0}return e=Dt.has(e)?e:d(e),t.getAttribute(e)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(t,e){return kt(t,e)}build(t,e,n,i){St(t,e,n,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,n,i){Rt(t,e,0,i)}mount(t){this.isSVGTag=At(t.tagName),super.mount(t)}}class ko extends Ro{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(O.has(e)){const t=ii(e);return t&&t.default||0}{const i=(n=t,window.getComputedStyle(n)),s=($(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return As(t,e)}build(t,e,n,i){ht(t,e,n,i.transformTemplate)}scrapeMotionValuesFromProps(t,e){return Lt(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;U(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=""+t)}))}renderInstance(t,e,n,i){Mt(t,e,n,i)}}function Bo(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=lo(t)?new Lo(e,{enableHardwareAcceleration:!1}):new ko(e,{enableHardwareAcceleration:!0});n.mount(t),so.set(t,n)}function jo(t,e,n){const i=U(t)?t:yi(t);return i.start(ui("",i,e,n)),i.animation}function Fo(t,e=100){const n=On({keyframes:[0,e],...t}),i=Math.min(Wn(n),2e4);return{type:"keyframes",ease:t=>n.next(i*t).value/e,duration:Se(i)}}function Oo(t,e,n,i){var s;return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(s=i.get(e))&&void 0!==s?s:t}const Io=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t};function Uo(t,e){return Oe(t)?t[Io(0,t.length,e)]:t}function Wo(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(pi(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:nn(s,o,i[r]),easing:Uo(n,r)})}function No(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function zo(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function Ho(t,e){return e[t]||(e[t]=[]),e[t]}function $o(t){return Array.isArray(t)?t:[t]}function Yo(t,e){return t[e]?{...t,...t[e]}:{...t}}const Xo=t=>"number"==typeof t,Go=t=>t.every(Xo);function qo(t,e,n,i){const s=io(t,i),o=s.length;Ee(Boolean(o));const r=[];for(let t=0;t<o;t++){const i=s[t];so.has(i)||Bo(i);const a=so.get(i),l={...n};"function"==typeof l.delay&&(l.delay=l.delay(t,o)),r.push(...Di(a,{...e,transition:l},{}))}return new ao(r)}function Zo(t,e,n){const i=[];return function(t,{defaultTransition:e={},...n}={},i){const s=e.duration||.3,o=new Map,r=new Map,a={},l=new Map;let u=0,c=0,h=0;for(let n=0;n<t.length;n++){const o=t[n];if("string"==typeof o){l.set(o,c);continue}if(!Array.isArray(o)){l.set(o.name,Oo(c,o.at,u,l));continue}let[d,p,m={}]=o;void 0!==m.at&&(c=Oo(c,m.at,u,l));let f=0;const g=(t,n,i,o=0,r=0)=>{const a=$o(t),{delay:l=0,times:u=Vn(a),type:d="keyframes",...p}=n;let{ease:m=e.ease||"easeOut",duration:g}=n;const y="function"==typeof l?l(o,r):l,v=a.length;if(v<=2&&"spring"===d){let t=100;if(2===v&&Go(a)){const e=a[1]-a[0];t=Math.abs(e)}const e={...p};void 0!==g&&(e.duration=be(g));const n=Fo(e,t);m=n.ease,g=n.duration}null!=g||(g=s);const x=c+y,P=x+g;1===u.length&&0===u[0]&&(u[1]=1);const w=u.length-a.length;w>0&&Cn(u,w),1===a.length&&a.unshift(null),Wo(i,a,m,u,x,P),f=Math.max(y+g,f),h=Math.max(P,h)};if(U(d)){g(p,m,Ho("default",zo(d,r)))}else{const t=io(d,i,a),e=t.length;for(let n=0;n<e;n++){p=p,m=m;const i=zo(t[n],r);for(const t in p)g(p[t],Yo(m,t),Ho(t,i),n,e)}}u=c,c+=f}return r.forEach((t,i)=>{for(const s in t){const r=t[s];r.sort(No);const a=[],l=[],u=[];for(let t=0;t<r.length;t++){const{at:e,value:n,easing:i}=r[t];a.push(n),l.push(bn(0,h,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),o.has(i)||o.set(i,{keyframes:{},transition:{}});const c=o.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:h,ease:u,times:l,...n}}}),o}(t,e,n).forEach(({keyframes:t,transition:e},n)=>{let s;s=U(n)?jo(n,t.default,e.default):qo(n,t,e),i.push(s)}),new ao(i)}const Ko=t=>function(e,n,i){let s;var o;return o=e,s=Array.isArray(o)&&Array.isArray(o[0])?Zo(e,n,t):function(t){return"object"==typeof t&&!Array.isArray(t)}(n)?qo(e,n,i,t):jo(e,n,i),t&&t.animations.push(s),s},_o=Ko(),Jo=new WeakMap;let Qo;function tr({target:t,contentRect:e,borderBoxSize:n}){var i;null===(i=Jo.get(t))||void 0===i||i.forEach(i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})})}function er(t){t.forEach(tr)}function nr(t,e){Qo||"undefined"!=typeof ResizeObserver&&(Qo=new ResizeObserver(er));const n=io(t);return n.forEach(t=>{let n=Jo.get(t);n||(n=new Set,Jo.set(t,n)),n.add(e),null==Qo||Qo.observe(t)}),()=>{n.forEach(t=>{const n=Jo.get(t);null==n||n.delete(e),(null==n?void 0:n.size)||null==Qo||Qo.unobserve(t)})}}const ir=new Set;let sr;function or(t){return ir.add(t),sr||(sr=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};ir.forEach(t=>t(e))},window.addEventListener("resize",sr)),()=>{ir.delete(t),!ir.size&&sr&&(sr=void 0)}}const rr={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ar(t,e,n,i){const s=n[e],{length:o,position:r}=rr[e],a=s.current,l=n.time;s.current=t["scroll"+r],s.scrollLength=t["scroll"+o]-t["client"+o],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=bn(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:Dn(s.current-a,u)}const lr={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},ur={start:0,center:.5,end:1};function cr(t,e,n=0){let i=0;if(void 0!==ur[t]&&(t=ur[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const hr=[0,0];function dr(t,e,n,i){let s=Array.isArray(t)?t:hr,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,ur[t]?t:"0"]),o=cr(s[0],n,i),r=cr(s[1],e),o-r}const pr={x:0,y:0};function mr(t,e,n){let{offset:i=lr.All}=n;const{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):pr,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=dr(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=An(e[o].offset,Vn(i)),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=e[o].interpolate(e[o].current)}function fr(t,e,n,i={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){ar(t,"x",e,n),ar(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&mr(t,n,i)},notify:()=>e(n)}}const gr=new WeakMap,yr=new WeakMap,vr=new WeakMap,xr=t=>t===document.documentElement?window:t;function Pr(t,{container:e=document.documentElement,...n}={}){let i=vr.get(e);i||(i=new Set,vr.set(e,i));const s=fr(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!gr.has(e)){const t=()=>{for(const t of i)t.measure()},n=()=>{for(const t of i)t.update(Yt.timestamp)},s=()=>{for(const t of i)t.notify()},a=()=>{Ht.read(t,!1,!0),Ht.read(n,!1,!0),Ht.update(s,!1,!0)};gr.set(e,a);const l=xr(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&yr.set(e,(r=a,"function"==typeof(o=e)?or(o):nr(o,r))),l.addEventListener("scroll",a,{passive:!0})}var o,r;const a=gr.get(e);return Ht.read(a,!1,!0),()=>{var t;$t(a);const n=vr.get(e);if(!n)return;if(n.delete(s),n.size)return;const i=gr.get(e);gr.delete(e),i&&(xr(e).removeEventListener("scroll",i),null===(t=yr.get(e))||void 0===t||t(),window.removeEventListener("resize",i))}}const wr=new Map;function Er({source:t=document.documentElement,axis:e="y"}={}){wr.has(t)||wr.set(t,{});const n=wr.get(t);return n[e]||(n[e]=ro()?new ScrollTimeline({source:t,axis:e}):function({source:t,axis:e="y"}){const n={value:0},i=Pr(t=>{n.value=100*t[e].progress},{container:t,axis:e});return{currentTime:n,cancel:i}}({source:t,axis:e})),n[e]}const br={some:0,all:1};function Sr(t,e,{root:n,margin:i,amount:s="some"}={}){const o=io(t),r=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else n&&(n(t),r.delete(t.target))})},{root:n,rootMargin:i,threshold:"number"==typeof s?s:br[s]});return o.forEach(t=>a.observe(t)),()=>a.disconnect()}function Tr(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=t[1+n],o=t[2+n],r=t[3+n],a=An(s,o,{mixer:(l=o[0],(t=>t&&"object"==typeof t&&t.mix)(l)?l.mix:void 0),...r});var l;return e?a(i):a}const Ar=Ht,Cr=zt.reduce((t,e)=>(t[e]=t=>$t(t),t),{}),Vr=["","X","Y","Z"],Mr={visibility:"hidden"};let Dr=0;const Rr={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Lr({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=Dr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{var t;this.projectionUpdateScheduled=!1,Rr.totalNodes=Rr.resolvedTargetDeltas=Rr.recalculatedProjection=0,this.nodes.forEach(jr),this.nodes.forEach(zr),this.nodes.forEach(Hr),this.nodes.forEach(Fr),t=Rr,window.MotionDebug&&window.MotionDebug.record(t)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new to)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new mi),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=lo(e),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=no(i,250),eo.hasAnimatedSinceResize&&(eo.hasAnimatedSinceResize=!1,this.nodes.forEach(Nr))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Zr,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!Zs(this.targetLayout,i)||n,u=!e&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...ai(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||Nr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,$t(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach($r),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Ir);this.isUpdating||this.nodes.forEach(Ur),this.isUpdating=!1,this.nodes.forEach(Wr),this.nodes.forEach(kr),this.nodes.forEach(Br),this.clearAllSnapshots();const t=performance.now();Yt.delta=G(0,1e3/60,t-Yt.timestamp),Yt.timestamp=t,Yt.isProcessing=!0,Xt.update.process(Yt),Xt.preRender.process(Yt),Xt.render.process(Yt),Yt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Or),this.sharedNodes.forEach(Yr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ht.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ht.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:n(this.instance)})}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!qs(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||ps(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Jr((i=n).x),Jr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox(),{scroll:n}=this.root;return n&&(ws(e.x,n.offset.x),ws(e.y,n.offset.y)),e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Ns(e,t);for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;if(i!==this.root&&s&&o.layoutScroll){if(s.isRoot){Ns(e,t);const{scroll:n}=this.root;n&&(ws(e.x,-n.offset.x),ws(e.y,-n.offset.y))}ws(e.x,s.offset.x),ws(e.y,s.offset.y)}}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Ns(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Ts(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),ps(i.latestValues)&&Ts(n,i.latestValues)}return ps(this.latestValues)&&Ts(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Ns(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!ps(n.latestValues))continue;ds(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};Ns(i,n.measurePageBox()),Xs(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return ps(this.latestValues)&&Xs(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Yt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=Yt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ns(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Ns(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var r,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,ts(r.x,a.x,l.x),ts(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Ns(this.target,this.layout.layoutBox),xs(this.target,this.targetDelta)):Ns(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ns(this.relativeTargetOrigin,this.target,t.target),Ns(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Rr.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!ds(this.parent.latestValues)&&!ms(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===Yt.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;Ns(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const s=o.instance;s&&s.style&&"contents"===s.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Ts(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,xs(t,r)),i&&ps(o.latestValues)&&Ts(t,o.latestValues))}e.x=Ps(e.x),e.y=Ps(e.y)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox);const{target:l}=e;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;Qi(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=Js(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===r&&this.treeScale.y===a||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Rr.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(qr));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,g;Xr(o.x,t.x,n),Xr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ns(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,g=n,Gr(p.x,m.x,f.x,g),Gr(p.y,m.y,f.y,g),h&&(l=this.relativeTarget,d=h,l.x.min===d.x.min&&l.x.max===d.x.max&&l.y.min===d.y.min&&l.y.max===d.y.max)&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),Ns(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=nn(0,void 0!==n.opacity?n.opacity:1,Os(i)),t.opacityExit=nn(void 0!==e.opacity?e.opacity:1,0,Is(i))):o&&(t.opacity=nn(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let s=0;s<ks;s++){const o=`border${Ls[s]}Radius`;let r=Fs(e,o),a=Fs(n,o);if(void 0===r&&void 0===a)continue;r||(r=0),a||(a=0);0===r||0===a||js(r)===js(a)?(t[o]=Math.max(nn(Bs(r),Bs(a),i),0),(st.test(a)||st.test(r))&&(t[o]+="%")):t[o]=a}(e.rotate||n.rotate)&&(t.rotate=nn(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&($t(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ht.update(()=>{eo.hasAnimatedSinceResize=!0,this.currentAnimation=jo(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Qr(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Ki(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Ki(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Ns(e,n),Ts(e,s),Qi(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new _s);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(e=!0),!e)return;const i={};for(let e=0;e<Vr.length;e++){const s="rotate"+Vr[e];n[s]&&(i[s]=n[s],t.setStaticValue(s,0))}t.render();for(const e in i)t.setStaticValue(e,i[e]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Mr;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=Ot(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Ot(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ps(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=Js(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const t in B){if(void 0===r[t])continue;const{correct:e,applyTo:n}=B[t],s="none"===i.transform?r[t]:e(r[t],o);if(n){const t=n.length;for(let e=0;e<t;e++)i[n[e]]=s}else i[t]=s}return this.options.layoutId&&(i.pointerEvents=o===this?Ot(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(Ir),this.root.sharedNodes.clear()}}}function kr(t){t.updateLayout()}function Br(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?us(t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Ki(i);i.min=e[t].min,i.max=i.min+s}):Qr(s,n.layoutBox,e)&&us(i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Ki(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Qi(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Qi(a,t.applyTransform(i,!0),n.measuredBox):Qi(a,e,n.layoutBox);const l=!qs(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};ns(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};ns(a,e,o.layoutBox),Zs(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function jr(t){Rr.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Fr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Or(t){t.clearSnapshot()}function Ir(t){t.clearMeasurements()}function Ur(t){t.isLayoutDirty=!1}function Wr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Nr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function zr(t){t.resolveTargetDelta()}function Hr(t){t.calcProjection()}function $r(t){t.resetRotation()}function Yr(t){t.removeLeadSnapshot()}function Xr(t,e,n){t.translate=nn(e.translate,0,n),t.scale=nn(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Gr(t,e,n,i){t.min=nn(e.min,n.min,i),t.max=nn(e.max,n.max,i)}function qr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Zr={duration:.45,ease:[.4,0,.1,1]},Kr=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),_r=Kr("applewebkit/")&&!Kr("chrome/")?Math.round:Wt;function Jr(t){t.min=_r(t.min),t.max=_r(t.max)}function Qr(t,e,n){return"position"===t||"preserve-aspect"===t&&!_i(Ks(e),Ks(n),.2)}const ta=Lr({attachResizeListener:(t,e)=>Kt(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ea={current:void 0},na=Lr({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ea.current){const t=new ta({});t.mount(window),t.setOptions({layoutScroll:!0}),ea.current=t}return ea.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),ia=t=>!t.isLayoutDirty&&t.willUpdate(!1);function sa(){const t=new Set,e=new WeakMap,n=()=>t.forEach(ia);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}function oa(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const ra={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!ot.test(t))return t;t=parseFloat(t)}return`${oa(t,e.target.x)}% ${oa(t,e.target.y)}%`}},aa={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=yn.parse(t);if(s.length>5)return i;const o=yn.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=nn(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};function la(){const t=e.useContext(l);if(null===t)return[!0,null];const{isPresent:n,onExitComplete:i,register:s}=t,o=e.useId();e.useEffect(()=>s(o),[]);return!n&&i?[!1,()=>i&&i(o)]:[!0]}class ua extends o.default.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;j(ha),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),eo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||Ht.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ca(t){const[n,i]=la(),s=e.useContext(A);return o.default.createElement(ua,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(C),isPresent:n,safeToRemove:i})}const ha={borderRadius:{...ra,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ra,borderTopRightRadius:ra,borderBottomLeftRadius:ra,borderBottomRightRadius:ra,boxShadow:aa},da={pan:{Feature:class extends le{constructor(){super(...arguments),this.removePointerDownListener=Wt}onPointerDown(t){this.session=new Hi(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Cs(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:Rs(t),onStart:Rs(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&Ht.update(()=>i(t,e))}}}mount(){this.removePointerDownListener=te(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends le{constructor(t){super(t),this.removeGroupControls=Wt,this.removeListeners=Wt,this.controls=new Ms(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Wt}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:na,MeasureLayout:ca}},pa=(t,e)=>k(t)?new Lo(e,{enableHardwareAcceleration:!1}):new ko(e,{enableHardwareAcceleration:!0}),ma={layout:{ProjectionNode:na,MeasureLayout:ca}},fa={...Wi,...ve,...da,...ma},ga=R((t,e)=>Zt(t,e,fa,pa));const ya=R(Zt);function va(){const t=e.useRef(!1);return c(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function xa(){const t=va(),[n,i]=e.useState(0),s=e.useCallback(()=>{t.current&&i(n+1)},[n]);return[e.useCallback(()=>Ht.postRender(s),[s]),n]}class Pa extends s.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function wa({children:t,isPresent:n}){const i=e.useId(),o=e.useRef(null),r=e.useRef({width:0,height:0,top:0,left:0});return e.useInsertionEffect(()=>{const{width:t,height:e,top:s,left:a}=r.current;if(n||!o.current||!t||!e)return;o.current.dataset.motionPopId=i;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`\n          [data-motion-pop-id="${i}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            top: ${s}px !important;\n            left: ${a}px !important;\n          }\n        `),()=>{document.head.removeChild(l)}},[n]),s.createElement(Pa,{isPresent:n,childRef:o,sizeRef:r},s.cloneElement(t,{ref:o}))}const Ea=({children:t,initial:n,isPresent:i,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:u})=>{const c=jt(ba),h=e.useId(),d=e.useMemo(()=>({id:h,initial:n,isPresent:i,custom:r,onExitComplete:t=>{c.set(t,!0);for(const t of c.values())if(!t)return;o&&o()},register:t=>(c.set(t,!1),()=>c.delete(t))}),a?void 0:[i]);return e.useMemo(()=>{c.forEach((t,e)=>c.set(e,!1))},[i]),s.useEffect(()=>{!i&&!c.size&&o&&o()},[i]),"popLayout"===u&&(t=s.createElement(wa,{isPresent:i},t)),s.createElement(l.Provider,{value:d},t)};function ba(){return new Map}function Sa(t){return e.useEffect(()=>()=>t(),[])}const Ta=t=>t.key||"";function Aa(t){return"function"==typeof t}const Ca=e.createContext(null),Va=t=>!0===t,Ma=({children:t,id:n,inherit:i=!0})=>{const o=e.useContext(A),r=e.useContext(Ca),[a,l]=xa(),u=e.useRef(null),c=o.id||r;null===u.current&&((t=>Va(!0===t)||"id"===t)(i)&&c&&(n=n?c+"-"+n:c),u.current={id:n,group:Va(i)&&o.group||sa()});const h=e.useMemo(()=>({...u.current,forceRender:a}),[l]);return s.createElement(A.Provider,{value:h},t)},Da=e.createContext(null);function Ra(t){return t.value}function La(t,e){return t.layout.min-e.layout.min}function ka(t){const n=jt(()=>yi(t)),{isStatic:i}=e.useContext(r);if(i){const[,i]=e.useState(t);e.useEffect(()=>n.on("change",i),[])}return n}function Ba(t,e){const n=ka(e()),i=()=>n.set(e());return i(),c(()=>{const e=()=>Ht.update(i,!1,!0),n=t.map(t=>t.on("change",e));return()=>{n.forEach(t=>t()),$t(i)}}),n}function ja(t,e,n,i){if("function"==typeof t)return function(t){fi.current=[],t();const e=Ba(fi.current,t);return fi.current=void 0,e}(t);const s="function"==typeof e?e:Tr(e,n,i);return Array.isArray(t)?Fa(t,s):Fa([t],([t])=>s(t))}function Fa(t,e){const n=jt(()=>[]);return Ba(t,()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)})}function Oa(t,e=0){return U(t)?t:ka(e)}const Ia={Group:e.forwardRef((function({children:t,as:n="ul",axis:i="y",onReorder:o,values:r,...a},l){const u=jt(()=>ga(n)),c=[],h=e.useRef(!1);Ee(Boolean(r));const d={axis:i,registerItem:(t,e)=>{const n=c.findIndex(e=>t===e.value);-1!==n?c[n].layout=e[i]:c.push({value:t,layout:e[i]}),c.sort(La)},updateOrder:(t,e,n)=>{if(h.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex(t=>t.value===e);if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=nn(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?function([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}(t,s,s+o):t}(c,t,e,n);c!==i&&(h.current=!0,o(i.map(Ra).filter(t=>-1!==r.indexOf(t))))}};return e.useEffect(()=>{h.current=!1}),s.createElement(u,{...a,ref:l,ignoreStrict:!0},s.createElement(Da.Provider,{value:d},t))})),Item:e.forwardRef((function({children:t,style:n={},value:i,as:o="li",onDrag:r,layout:a=!0,...l},u){const c=jt(()=>ga(o)),h=e.useContext(Da),d={x:Oa(n.x),y:Oa(n.y)},p=ja([d.x,d.y],([t,e])=>t||e?1:"unset");Ee(Boolean(h));const{axis:m,registerItem:f,updateOrder:g}=h;return s.createElement(c,{drag:m,...l,dragSnapToOrigin:!0,style:{...n,x:d.x,y:d.y,zIndex:p},layout:a,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&g(i,d[m].get(),n[m]),r&&r(t,e)},onLayoutMeasure:t=>f(i,t),ref:u,ignoreStrict:!0},t)}))},Ua={renderer:pa,...Wi,...ve},Wa={...Ua,...da,...ma};function Na(t,n,i){e.useInsertionEffect(()=>t.on(n,i),[t,n,i])}function za(t,e){we(Boolean(!e||e.current))}const Ha=()=>({scrollX:yi(0),scrollY:yi(0),scrollXProgress:yi(0),scrollYProgress:yi(0)});function $a({container:t,target:n,layoutEffect:i=!0,...s}={}){const o=jt(Ha);return(i?c:e.useEffect)(()=>(za(0,n),za(0,t),Pr(({x:t,y:e})=>{o.scrollX.set(t.current),o.scrollXProgress.set(t.progress),o.scrollY.set(e.current),o.scrollYProgress.set(e.progress)},{...s,container:(null==t?void 0:t.current)||void 0,target:(null==n?void 0:n.current)||void 0})),[t,n,JSON.stringify(s.offset)]),o}function Ya(t){const n=e.useRef(0),{isStatic:i}=e.useContext(r);e.useEffect(()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return Ht.update(e,!0),()=>$t(e)},[t])}class Xa extends gi{constructor(){super(...arguments),this.members=[],this.transforms=new Set}add(t){let e;O.has(t)?(this.transforms.add(t),e="transform"):t.startsWith("origin")||$(t)||"willChange"===t||(e=d(t)),e&&(di(this.members,e),this.update())}remove(t){O.has(t)?(this.transforms.delete(t),this.transforms.size||pi(this.members,"transform")):pi(this.members,d(t)),this.update()}update(){this.set(this.members.length?this.members.join(", "):"auto")}}function Ga(){!So.current&&To();const[t]=e.useState(bo.current);return t}function qa(){let t=!1;const e=new Set,n={subscribe:t=>(e.add(t),()=>{e.delete(t)}),start(n,i){Ee(t);const s=[];return e.forEach(t=>{s.push(ki(t,n,{transitionOverride:i}))}),Promise.all(s)},set:n=>(Ee(t),e.forEach(t=>{!function(t,e){Array.isArray(e)?Si(t,e):"string"==typeof e?Si(t,[e]):bi(t,e)}(t,n)})),stop(){e.forEach(t=>{!function(t){t.values.forEach(t=>t.stop())}(t)})},mount:()=>(t=!0,()=>{t=!1,n.stop()})};return n}function Za(){const t=jt(qa);return c(t.mount,[]),t}const Ka=Za;class _a{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}}const Ja=()=>new _a;function Qa(t){return null!==t&&"object"==typeof t&&V in t}function tl(){return el}function el(t){ea.current&&(ea.current.isUpdating=!1,ea.current.blockUpdate(),t&&t())}const nl=(t,e)=>`${t}: ${e}`,il=new Map;let sl,ol,rl;function al(t,e,n,i){const s=O.has(e)?"transform":e,o=nl(t,s),r=il.get(o);if(!r)return null;const{animation:a,startTime:l}=r;return null===l||window.HandoffComplete?((()=>{il.delete(o);try{a.cancel()}catch(t){}})(),null):(void 0===sl&&(sl=performance.now()),sl-l||0)}const ll=()=>({});class ul extends Do{build(){}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...n}){return Ti(this,n,Ci(n,t||{},this)),{transition:t,transitionEnd:e,...n}}}const cl=It({scrapeMotionValuesFromProps:ll,createRenderState:ll});const hl=t=>t>.001?1/t:1e5;let dl=!1;let pl=0;t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:o,exitBeforeEnter:r,presenceAffectsLayout:a=!0,mode:l="sync"})=>{Ee(!r);const u=e.useContext(A).forceRender||xa()[0],h=va(),d=function(t){const n=[];return e.Children.forEach(t,t=>{e.isValidElement(t)&&n.push(t)}),n}(t);let p=d;const m=e.useRef(new Map).current,f=e.useRef(p),g=e.useRef(new Map).current,y=e.useRef(!0);if(c(()=>{y.current=!1,function(t,e){t.forEach(t=>{const n=Ta(t);e.set(n,t)})}(d,g),f.current=p}),Sa(()=>{y.current=!0,g.clear(),m.clear()}),y.current)return s.createElement(s.Fragment,null,p.map(t=>s.createElement(Ea,{key:Ta(t),isPresent:!0,initial:!!i&&void 0,presenceAffectsLayout:a,mode:l},t)));p=[...p];const v=f.current.map(Ta),x=d.map(Ta),P=v.length;for(let t=0;t<P;t++){const e=v[t];-1!==x.indexOf(e)||m.has(e)||m.set(e,void 0)}return"wait"===l&&m.size&&(p=[]),m.forEach((t,e)=>{if(-1!==x.indexOf(e))return;const i=g.get(e);if(!i)return;const r=v.indexOf(e);let c=t;if(!c){const t=()=>{m.delete(e);const t=Array.from(g.keys()).filter(t=>!x.includes(t));if(t.forEach(t=>g.delete(t)),f.current=d.filter(n=>{const i=Ta(n);return i===e||t.includes(i)}),!m.size){if(!1===h.current)return;u(),o&&o()}};c=s.createElement(Ea,{key:Ta(i),isPresent:!1,onExitComplete:t,custom:n,presenceAffectsLayout:a,mode:l},i),m.set(e,c)}p.splice(r,0,c)}),p=p.map(t=>{const e=t.key;return m.has(e)?t:s.createElement(Ea,{key:Ta(t),isPresent:!0,presenceAffectsLayout:a,mode:l},t)}),s.createElement(s.Fragment,null,m.size?p:p.map(t=>e.cloneElement(t)))},t.AnimateSharedLayout=({children:t})=>(s.useEffect(()=>{Ee(!1)},[]),s.createElement(Ma,{id:jt(()=>"asl-"+pl++)},t)),t.DeprecatedLayoutGroupContext=Ca,t.DragControls=_a,t.FlatTree=to,t.LayoutGroup=Ma,t.LayoutGroupContext=A,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,o]=e.useState(!Aa(n)),r=e.useRef(void 0);if(!Aa(n)){const{renderer:t,...e}=n;r.current=t,T(e)}return e.useEffect(()=>{Aa(n)&&n().then(({renderer:t,...e})=>{T(e),r.current=t,o(!0)})},[]),s.createElement(h.Provider,{value:{renderer:r.current,strict:i}},t)},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&xt(n),(i={...e.useContext(r),...i}).isStatic=jt(()=>i.isStatic);const o=e.useMemo(()=>i,[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return s.createElement(r.Provider,{value:o},t)},t.MotionConfigContext=r,t.MotionContext=a,t.MotionGlobalConfig=li,t.MotionValue=gi,t.PresenceContext=l,t.Reorder=Ia,t.SwitchLayoutGroupContext=C,t.VisualElement=Do,t.addPointerEvent=te,t.addPointerInfo=Qt,t.addScaleCorrector=j,t.animate=_o,t.animateValue=zn,t.animateVisualElement=ki,t.animationControls=qa,t.animations=Wi,t.anticipate=Xe,t.backIn=$e,t.backInOut=Ye,t.backOut=He,t.buildTransform=z,t.calcLength=Ki,t.cancelFrame=$t,t.cancelSync=Cr,t.checkTargetForNewValues=Ti,t.circIn=We,t.circInOut=ze,t.circOut=Ne,t.clamp=G,t.color=en,t.complex=yn,t.createBox=ls,t.createDomMotionComponent=function(t){return M(Zt(t,{forwardMotionProps:!1},fa,pa))},t.createMotionComponent=M,t.createScopedAnimate=Ko,t.cubicBezier=ke,t.delay=no,t.disableInstantTransitions=function(){Te.current=!1},t.distance=Ni,t.distance2D=zi,t.domAnimation=Ua,t.domMax=Wa,t.easeIn=Be,t.easeInOut=Fe,t.easeOut=je,t.filterProps=Pt,t.frame=Ht,t.frameData=Yt,t.inView=Sr,t.interpolate=An,t.invariant=Ee,t.isBrowser=u,t.isDragActive=ae,t.isMotionComponent=Qa,t.isMotionValue=U,t.isValidMotionProp=yt,t.m=ya,t.makeUseVisualState=It,t.mirrorEasing=Ie,t.mix=nn,t.motion=ga,t.motionValue=yi,t.optimizedAppearDataAttribute=p,t.pipe=ne,t.progress=bn,t.px=ot,t.resolveMotionValue=Ot,t.reverseEasing=Ue,t.scroll=function(t,e){const n=Er(e);return"function"==typeof t?oo(t,n):t.attachTimeline(n)},t.scrollInfo=Pr,t.spring=On,t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=qe(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.HandoffComplete)return void(window.HandoffAppearAnimations=void 0);const o=t.dataset.framerAppearId;if(!o)return;window.HandoffAppearAnimations=al;const r=nl(o,e);rl||(rl=Re(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),il.set(r,{animation:rl,startTime:null}));const a=()=>{rl.cancel();const o=Re(t,e,n,i);void 0===ol&&(ol=performance.now()),o.startTime=ol,il.set(r,{animation:o,startTime:ol}),s&&s(o)};rl.ready?rl.ready.then(a).catch(Wt):a()},t.steps=Xt,t.sync=Ar,t.transform=Tr,t.unwrapMotionComponent=function(t){if(Qa(t))return t[V]},t.useAnimate=function(){const t=jt(()=>({current:null,animations:[]})),e=jt(()=>Ko(t));return Sa(()=>{t.animations.forEach(t=>t.stop())}),[t,e]},t.useAnimation=Ka,t.useAnimationControls=Za,t.useAnimationFrame=Ya,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]);return[i,e.useCallback(e=>{n.current="number"!=typeof e?Io(0,t.length,n.current+1):e,s(t[n.current])},[t.length,...t])]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=cl({},!1),o=jt(()=>new ul({props:{},visualState:s,presenceContext:null},{initialState:t}));return e.useEffect(()=>(o.mount({}),()=>o.unmount()),[o]),e.useEffect(()=>{o.update({onUpdate:t=>{i({...t})}},null)},[i,o]),[n,jt(()=>t=>ki(o,t))]},t.useDeprecatedInvertedScale=function(t){let n=ka(1),i=ka(1);const{visualElement:s}=e.useContext(a);return Ee(!(!t&&!s)),we(dl),dl=!0,t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:ja(n,hl),scaleY:ja(i,hl)}},t.useDomEvent=function(t,n,i,s){e.useEffect(()=>{const e=t.current;if(i&&e)return Kt(e,n,i,s)},[t,n,i,s])},t.useDragControls=function(){return jt(Ja)},t.useElementScroll=function(t){return $a({container:t})},t.useForceUpdate=xa,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1}={}){const[r,a]=e.useState(!1);return e.useEffect(()=>{if(!t.current||o&&r)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return Sr(t.current,()=>(a(!0),o?void 0:()=>a(!1)),e)},[n,t,i,o,s]),r},t.useInstantLayoutTransition=tl,t.useInstantTransition=function(){const[t,n]=xa(),i=tl(),s=e.useRef();return e.useEffect(()=>{Ht.postRender(()=>Ht.postRender(()=>{n===s.current&&(Te.current=!1)}))},[n]),e=>{i(()=>{Te.current=!0,t(),e(),s.current=n+1})}},t.useIsPresent=function(){return null===(t=e.useContext(l))||t.isPresent;var t},t.useIsomorphicLayoutEffect=c,t.useMotionTemplate=function(t,...e){const n=t.length;return Ba(e.filter(U),(function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=U(n)?n.get():n)}return i}))},t.useMotionValue=ka,t.useMotionValueEvent=Na,t.usePresence=la,t.useReducedMotion=Ga,t.useReducedMotionConfig=function(){const t=Ga(),{reducedMotion:n}=e.useContext(r);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return s.useCallback(()=>{const t=ea.current;t&&t.resetTree()},[])},t.useScroll=$a,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(r),s=e.useRef(null),o=ka(U(t)?t.get():t),a=()=>{s.current&&s.current.stop()};return e.useInsertionEffect(()=>o.attach((t,e)=>{if(i)return e(t);if(a(),s.current=zn({keyframes:[o.get(),t],velocity:o.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:e}),!Yt.isProcessing){const t=performance.now()-Yt.timestamp;t<30&&(s.current.time=Se(t))}return o.get()},a),[JSON.stringify(n)]),c(()=>{if(U(t))return t.on("change",t=>o.set(parseFloat(t)))},[o]),o},t.useTime=function(){const t=ka(0);return Ya(e=>t.set(e)),t},t.useTransform=ja,t.useUnmountEffect=Sa,t.useVelocity=function(t){const e=ka(t.getVelocity());return Na(t,"velocityChange",t=>{e.set(t)}),e},t.useViewportScroll=function(){return $a()},t.useWillChange=function(){return jt(()=>new Xa("auto"))},t.visualElementStore=so,t.warning=we,t.wrap=Io,Object.defineProperty(t,"__esModule",{value:!0})}));
