import React, { useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, RotateCcw, MessageSquare, Minimize2, Maximize2 } from 'lucide-react';
import { ChatMessage } from './ChatMessage';
import { ChatInput } from './ChatInput';
import { TypingIndicator } from './TypingIndicator';
import { useChatStore } from '../../stores/useChatStore';
import { useThemeStore } from '../../stores/useThemeStore';

interface ChatBotProps {
  isOpen: boolean;
  onToggle: () => void;
  isMinimized: boolean;
  onMinimize: () => void;
}

export const ChatBot: React.FC<ChatBotProps> = ({ 
  isOpen, 
  onToggle, 
  isMinimized, 
  onMinimize 
}) => {
  const { 
    messages, 
    isTyping, 
    processUserMessage, 
    clearChat,
    createNewSession 
  } = useChatStore();
  const isDark = useThemeStore(state => state.isDark);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);
  
  // Initialize with welcome message if no messages
  useEffect(() => {
    if (messages.length === 0) {
      const timer = setTimeout(() => {
        processUserMessage('hello');
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);
  
  const handleSendMessage = async (message: string) => {
    await processUserMessage(message);
  };
  
  const handleClearChat = () => {
    clearChat();
    createNewSession();
    // Send welcome message after clearing
    setTimeout(() => {
      processUserMessage('hello');
    }, 500);
  };
  
  if (!isOpen) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9, y: 20 }}
      animate={{ 
        opacity: 1, 
        scale: 1, 
        y: 0,
        height: isMinimized ? '60px' : '600px'
      }}
      exit={{ opacity: 0, scale: 0.9, y: 20 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className={`fixed bottom-4 right-4 w-96 bg-white/90 dark:bg-gray-900/90 backdrop-blur-lg border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-2xl z-50 flex flex-col overflow-hidden ${
        isMinimized ? 'h-15' : 'h-[600px]'
      }`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-blue-500/10 to-emerald-500/10">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
            <MessageSquare className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">ShopBot Assistant</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {isTyping ? 'Typing...' : 'Online'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleClearChat}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            title="Clear Chat"
          >
            <RotateCcw className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={onMinimize}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            title={isMinimized ? 'Maximize' : 'Minimize'}
          >
            {isMinimized ? (
              <Maximize2 className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            ) : (
              <Minimize2 className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            )}
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={onToggle}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            title="Close Chat"
          >
            <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          </motion.button>
        </div>
      </div>
      
      {/* Chat Messages */}
      <AnimatePresence>
        {!isMinimized && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50/30 dark:bg-gray-800/30"
          >
            {messages.map((message, index) => (
              <ChatMessage 
                key={message.id} 
                message={message} 
                isLast={index === messages.length - 1}
              />
            ))}
            
            {isTyping && <TypingIndicator />}
            
            <div ref={messagesEndRef} />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Chat Input */}
      <AnimatePresence>
        {!isMinimized && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
          >
            <ChatInput 
              onSendMessage={handleSendMessage}
              disabled={isTyping}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};