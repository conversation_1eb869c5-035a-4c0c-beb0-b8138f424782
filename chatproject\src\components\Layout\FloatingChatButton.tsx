import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X } from 'lucide-react';
import { ChatBot } from '../Chat/ChatBot';

export const FloatingChatButton: React.FC = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  
  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
    setIsMinimized(false);
  };
  
  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };
  
  return (
    <>
      {/* Floating Button */}
      <AnimatePresence>
        {!isChatOpen && (
          <motion.button
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleChat}
            className="fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-blue-500 to-emerald-500 hover:from-blue-600 hover:to-emerald-600 text-white rounded-full shadow-2xl flex items-center justify-center z-40 transition-all duration-300"
          >
            <MessageCircle className="w-6 h-6" />
            
            {/* Notification Pulse */}
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-xs font-bold text-white">!</span>
            </div>
            
            {/* Ripple Effect */}
            <div className="absolute inset-0 rounded-full bg-blue-500 animate-ping opacity-30"></div>
          </motion.button>
        )}
      </AnimatePresence>
      
      {/* Chat Bot */}
      <ChatBot 
        isOpen={isChatOpen}
        onToggle={toggleChat}
        isMinimized={isMinimized}
        onMinimize={toggleMinimize}
      />
    </>
  );
};