import { create } from 'zustand';
import { ChatMessage, Product, ChatSession } from '../types';
import { allProducts } from '../data/mockProducts';

interface ChatState {
  messages: ChatMessage[];
  isTyping: boolean;
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  processUserMessage: (content: string) => Promise<void>;
  clearChat: () => void;
  setTyping: (isTyping: boolean) => void;
  createNewSession: () => void;
  loadSession: (sessionId: string) => void;
}

const generateId = () => Math.random().toString(36).substr(2, 9);

const searchProducts = (query: string, filters?: any): Product[] => {
  const lowercaseQuery = query.toLowerCase();
  let results = allProducts.filter(product => 
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.description.toLowerCase().includes(lowercaseQuery) ||
    product.category.toLowerCase().includes(lowercaseQuery) ||
    product.brand?.toLowerCase().includes(lowercaseQuery)
  );

  if (filters) {
    if (filters.category && filters.category !== 'all') {
      results = results.filter(p => p.category.toLowerCase() === filters.category.toLowerCase());
    }
    if (filters.maxPrice) {
      results = results.filter(p => p.price <= filters.maxPrice);
    }
    if (filters.minRating) {
      results = results.filter(p => p.rating >= filters.minRating);
    }
    if (filters.availability) {
      results = results.filter(p => p.availability);
    }
  }

  return results.slice(0, 12);
};

const generateBotResponse = (userMessage: string): { content: string; products?: Product[]; type?: string } => {
  const message = userMessage.toLowerCase();
  
  // Greeting responses
  if (message.includes('hello') || message.includes('hi') || message.includes('hey')) {
    return {
      content: "Hello! 👋 Welcome to our store! I'm your personal shopping assistant. I can help you find products, answer questions, and guide you through your purchase. What are you looking for today?",
      type: 'text'
    };
  }
  
  // Help responses
  if (message.includes('help')) {
    return {
      content: "I'm here to help! You can ask me to:\n\n• Find products by name or category\n• Show items under a certain price\n• Filter by ratings or availability\n• Get product recommendations\n• Compare different products\n\nTry asking something like 'Show me laptops under $1000' or 'Find books by James Clear'",
      type: 'text'
    };
  }
  
  // Price-based searches
  const priceMatch = message.match(/under [\$]?(\d+)/);
  if (priceMatch) {
    const maxPrice = parseInt(priceMatch[1]);
    const products = searchProducts('', { maxPrice });
    return {
      content: `Here are products under $${maxPrice}:`,
      products,
      type: 'products'
    };
  }
  
  // Category-based searches
  const categories = ['electronics', 'books', 'fashion', 'fitness', 'home', 'kitchen'];
  const foundCategory = categories.find(cat => message.includes(cat));
  if (foundCategory) {
    const products = searchProducts('', { category: foundCategory });
    return {
      content: `Here are ${foundCategory} products I found:`,
      products,
      type: 'products'
    };
  }
  
  // Brand searches
  const brands = ['apple', 'samsung', 'sony', 'nike', 'canon', 'dell'];
  const foundBrand = brands.find(brand => message.includes(brand));
  if (foundBrand) {
    const products = searchProducts(foundBrand);
    return {
      content: `Here are ${foundBrand} products:`,
      products,
      type: 'products'
    };
  }
  
  // Rating-based searches
  if (message.includes('high rated') || message.includes('best rated') || message.includes('top rated')) {
    const products = searchProducts('', { minRating: 4.5 });
    return {
      content: "Here are our highest-rated products:",
      products,
      type: 'products'
    };
  }
  
  // General product search
  const products = searchProducts(userMessage);
  if (products.length > 0) {
    return {
      content: `I found ${products.length} products matching "${userMessage}":`,
      products,
      type: 'products'
    };
  }
  
  // Default response
  return {
    content: `I couldn't find specific products for "${userMessage}". Try searching for categories like "electronics", "books", or "laptops", or ask for help to see what I can do!`,
    type: 'text'
  };
};

export const useChatStore = create<ChatState>((set, get) => ({
  messages: [],
  isTyping: false,
  currentSession: null,
  sessions: [],
  
  addMessage: (message) => {
    const newMessage: ChatMessage = {
      ...message,
      id: generateId(),
      timestamp: new Date()
    };
    
    set(state => ({
      messages: [...state.messages, newMessage]
    }));
    
    // Update current session
    const { currentSession } = get();
    if (currentSession) {
      const updatedSession = {
        ...currentSession,
        messages: [...currentSession.messages, newMessage],
        updatedAt: new Date()
      };
      
      set(state => ({
        currentSession: updatedSession,
        sessions: state.sessions.map(s => 
          s.id === updatedSession.id ? updatedSession : s
        )
      }));
    }
  },
  
  processUserMessage: async (content) => {
    const { addMessage, setTyping } = get();
    
    // Add user message
    addMessage({
      content,
      sender: 'user'
    });
    
    // Show typing indicator
    setTyping(true);
    
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000));
    
    // Generate bot response
    const response = generateBotResponse(content);
    
    // Add bot message
    addMessage({
      content: response.content,
      sender: 'bot',
      products: response.products,
      type: response.type as any
    });
    
    setTyping(false);
  },
  
  clearChat: () => {
    set({ messages: [] });
    get().createNewSession();
  },
  
  setTyping: (isTyping) => {
    set({ isTyping });
  },
  
  createNewSession: () => {
    const newSession: ChatSession = {
      id: generateId(),
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    set(state => ({
      currentSession: newSession,
      sessions: [newSession, ...state.sessions],
      messages: []
    }));
  },
  
  loadSession: (sessionId) => {
    const { sessions } = get();
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      set({
        currentSession: session,
        messages: session.messages
      });
    }
  }
}));