{"name": "eslint-plugin-react-hooks", "description": "ESLint rules for React Hooks", "version": "5.1.0-rc-fb9a90fa48-20240614", "repository": {"type": "git", "url": "https://github.com/facebook/react.git", "directory": "packages/eslint-plugin-react-hooks"}, "files": ["LICENSE", "README.md", "index.js", "cjs"], "keywords": ["eslint", "eslint-plugin", "eslintplugin", "react"], "license": "MIT", "bugs": {"url": "https://github.com/facebook/react/issues"}, "engines": {"node": ">=10"}, "homepage": "https://react.dev/", "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0"}, "devDependencies": {"@babel/eslint-parser": "^7.11.4", "@typescript-eslint/parser-v2": "npm:@typescript-eslint/parser@^2.26.0", "@typescript-eslint/parser-v3": "npm:@typescript-eslint/parser@^3.10.0", "@typescript-eslint/parser-v4": "npm:@typescript-eslint/parser@^4.1.0", "@typescript-eslint/parser-v5": "npm:@typescript-eslint/parser@^5.0.0-0", "babel-eslint": "^10.0.3", "eslint-v7": "npm:eslint@^7.7.0", "eslint-v9": "npm:eslint@^9.0.0"}}