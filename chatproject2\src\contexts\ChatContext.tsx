
import React, { createContext, useContext, useState } from 'react';
import { ChatContextType, ChatMessage } from '@/types';

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

export const ChatProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      content: 'Hello! Welcome to TechShop! I\'m your personal shopping assistant. I can help you find the perfect products, check availability, and answer any questions. What are you looking for today?',
      sender: 'bot',
      timestamp: new Date(),
      type: 'text'
    }
  ]);
  const [isTyping, setIsTyping] = useState(false);

  const addMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, newMessage]);
  };

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        content: 'Hello! Welcome to TechShop! I\'m your personal shopping assistant. I can help you find the perfect products, check availability, and answer any questions. What are you looking for today?',
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      }
    ]);
  };

  return (
    <ChatContext.Provider value={{ messages, addMessage, clearChat, isTyping, setIsTyping }}>
      {children}
    </ChatContext.Provider>
  );
};
