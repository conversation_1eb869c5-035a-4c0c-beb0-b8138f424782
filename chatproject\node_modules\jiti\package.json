{"name": "jiti", "version": "1.21.6", "description": "Runtime typescript and ESM support for Node.js", "repository": "unjs/jiti", "license": "MIT", "main": "./lib/index.js", "types": "dist/jiti.d.ts", "bin": "bin/jiti.js", "files": ["lib", "dist", "register.js"], "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "test:bun": "bun --bun test test/bun"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.24.7", "@babel/types": "^7.24.7", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/node": "^20.14.2", "@types/object-hash": "^3.0.6", "@types/resolve": "^1.20.6", "@types/semver": "^7.5.8", "@vitest/coverage-v8": "^1.6.0", "acorn": "^8.11.3", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "changelogen": "^0.5.5", "config": "^3.3.11", "create-require": "^1.1.1", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.4.0", "eslint-config-unjs": "^0.3.2", "esm": "^3.2.25", "estree-walker": "^3.0.3", "execa": "^9.1.0", "fast-glob": "^3.3.2", "mlly": "^1.7.1", "object-hash": "^3.0.0", "pathe": "^1.1.2", "pirates": "^4.0.6", "pkg-types": "^1.1.1", "prettier": "^3.3.1", "reflect-metadata": "^0.2.1", "semver": "^7.6.2", "std-env": "^3.7.0", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.5.1", "tslib": "^2.6.3", "typescript": "^5.4.5", "vite": "^5.2.12", "vitest": "^1.6.0", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}, "packageManager": "pnpm@9.2.0"}