import React from 'react';
import { motion } from 'framer-motion';
import { Bot, User } from 'lucide-react';
import { ChatMessage as ChatMessageType } from '../../types';
import { ProductCard } from '../Products/ProductCard';

interface ChatMessageProps {
  message: ChatMessageType;
  isLast?: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, isLast }) => {
  const isBot = message.sender === 'bot';
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex items-start space-x-3 ${isBot ? '' : 'flex-row-reverse space-x-reverse'}`}
    >
      {/* Avatar */}
      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
        isBot 
          ? 'bg-gradient-to-r from-blue-500 to-purple-500' 
          : 'bg-gradient-to-r from-emerald-500 to-teal-500'
      }`}>
        {isBot ? (
          <Bot className="w-4 h-4 text-white" />
        ) : (
          <User className="w-4 h-4 text-white" />
        )}
      </div>
      
      {/* Message Content */}
      <div className={`flex-1 max-w-xs ${isBot ? '' : 'flex flex-col items-end'}`}>
        <div
          className={`p-3 rounded-2xl ${
            isBot
              ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
              : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
          }`}
        >
          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
        </div>
        
        {/* Products */}
        {message.products && message.products.length > 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="mt-3 w-full max-w-lg"
          >
            <div className="grid grid-cols-1 gap-3">
              {message.products.slice(0, 3).map((product, index) => (
                <div key={product.id} className="transform scale-90 origin-left">
                  <ProductCard product={product} index={index} />
                </div>
              ))}
            </div>
            {message.products.length > 3 && (
              <div className="mt-2 text-center">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  +{message.products.length - 3} more products available
                </span>
              </div>
            )}
          </motion.div>
        )}
        
        {/* Timestamp */}
        <div className={`mt-1 text-xs text-gray-500 dark:text-gray-400 ${isBot ? '' : 'text-right'}`}>
          {new Date(message.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </div>
    </motion.div>
  );
};