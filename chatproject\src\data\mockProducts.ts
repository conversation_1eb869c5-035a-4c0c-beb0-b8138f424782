import { Product } from '../types';

export const mockProducts: Product[] = [
  // Electronics - Laptops
  {
    id: 'laptop-001',
    name: 'MacBook Pro 14" M3 Pro',
    description: 'Professional laptop with M3 Pro chip, 18GB RAM, 512GB SSD. Perfect for developers and creators.',
    price: 1999,
    category: 'Electronics',
    rating: 4.8,
    image_url: 'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 15,
    brand: 'Apple',
    specs: { processor: 'M3 Pro', ram: '18GB', storage: '512GB SSD' }
  },
  {
    id: 'laptop-002',
    name: 'Dell XPS 13 Plus',
    description: 'Ultra-portable laptop with Intel i7, 16GB RAM, 1TB SSD. Stunning OLED display.',
    price: 1599,
    category: 'Electronics',
    rating: 4.6,
    image_url: 'https://images.pexels.com/photos/18105/pexels-photo.jpg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 8,
    brand: 'Dell',
    specs: { processor: 'Intel i7', ram: '16GB', storage: '1TB SSD' }
  },
  {
    id: 'laptop-003',
    name: 'ThinkPad X1 Carbon Gen 11',
    description: 'Business laptop with Intel i7, 32GB RAM, 1TB SSD. Military-grade durability.',
    price: 1899,
    category: 'Electronics',
    rating: 4.7,
    image_url: 'https://images.pexels.com/photos/1229861/pexels-photo-1229861.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 12,
    brand: 'Lenovo',
    specs: { processor: 'Intel i7', ram: '32GB', storage: '1TB SSD' }
  },
  
  // Electronics - Smartphones
  {
    id: 'phone-001',
    name: 'iPhone 15 Pro Max',
    description: 'Latest iPhone with A17 Pro chip, 256GB storage, Pro camera system.',
    price: 1199,
    category: 'Electronics',
    rating: 4.9,
    image_url: 'https://images.pexels.com/photos/788946/pexels-photo-788946.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 25,
    brand: 'Apple',
    specs: { processor: 'A17 Pro', storage: '256GB', camera: '48MP Pro' }
  },
  {
    id: 'phone-002',
    name: 'Samsung Galaxy S24 Ultra',
    description: 'Premium Android phone with S Pen, 12GB RAM, 512GB storage.',
    price: 1299,
    category: 'Electronics',
    rating: 4.7,
    image_url: 'https://images.pexels.com/photos/1841841/pexels-photo-1841841.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 18,
    brand: 'Samsung',
    specs: { processor: 'Snapdragon 8 Gen 3', ram: '12GB', storage: '512GB' }
  },
  {
    id: 'phone-003',
    name: 'Google Pixel 8 Pro',
    description: 'AI-powered photography, pure Android experience, 12GB RAM, 256GB storage.',
    price: 999,
    category: 'Electronics',
    rating: 4.6,
    image_url: 'https://images.pexels.com/photos/1649771/pexels-photo-1649771.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 22,
    brand: 'Google',
    specs: { processor: 'Tensor G3', ram: '12GB', storage: '256GB' }
  },

  // Electronics - Headphones
  {
    id: 'headphones-001',
    name: 'AirPods Pro 2nd Gen',
    description: 'Premium wireless earbuds with active noise cancellation and spatial audio.',
    price: 249,
    category: 'Electronics',
    rating: 4.8,
    image_url: 'https://images.pexels.com/photos/3394650/pexels-photo-3394650.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 45,
    brand: 'Apple',
    specs: { type: 'Wireless', features: 'ANC, Spatial Audio' }
  },
  {
    id: 'headphones-002',
    name: 'Sony WH-1000XM5',
    description: 'Industry-leading noise canceling wireless headphones with 30-hour battery.',
    price: 399,
    category: 'Electronics',
    rating: 4.7,
    image_url: 'https://images.pexels.com/photos/3394656/pexels-photo-3394656.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 30,
    brand: 'Sony',
    specs: { type: 'Over-ear', battery: '30 hours', features: 'ANC' }
  },

  // Books - Fiction
  {
    id: 'book-001',
    name: 'The Midnight Library',
    description: 'A novel about life, hope, and the infinite possibilities that exist within each of us.',
    price: 16.99,
    category: 'Books',
    rating: 4.5,
    image_url: 'https://images.pexels.com/photos/1029141/pexels-photo-1029141.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 50,
    brand: 'Penguin Random House',
    specs: { author: 'Matt Haig', pages: 288, genre: 'Fiction' }
  },
  {
    id: 'book-002',
    name: 'Where the Crawdads Sing',
    description: 'A coming-of-age mystery novel about a young woman who raised herself in the marshes.',
    price: 14.99,
    category: 'Books',
    rating: 4.6,
    image_url: 'https://images.pexels.com/photos/1481409/pexels-photo-1481409.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 35,
    brand: 'G.P. Putnam\'s Sons',
    specs: { author: 'Delia Owens', pages: 384, genre: 'Mystery Fiction' }
  },

  // Books - Non-Fiction
  {
    id: 'book-003',
    name: 'Atomic Habits',
    description: 'An Easy & Proven Way to Build Good Habits & Break Bad Ones.',
    price: 18.99,
    category: 'Books',
    rating: 4.8,
    image_url: 'https://images.pexels.com/photos/1166657/pexels-photo-1166657.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 40,
    brand: 'Avery',
    specs: { author: 'James Clear', pages: 320, genre: 'Self-Help' }
  },
  {
    id: 'book-004',
    name: 'Sapiens: A Brief History of Humankind',
    description: 'From the evolution of Homo sapiens to the present day.',
    price: 19.99,
    category: 'Books',
    rating: 4.7,
    image_url: 'https://images.pexels.com/photos/1370295/pexels-photo-1370295.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 28,
    brand: 'Harper',
    specs: { author: 'Yuval Noah Harari', pages: 512, genre: 'History' }
  },

  // More Electronics
  {
    id: 'tablet-001',
    name: 'iPad Pro 12.9" M2',
    description: 'Professional tablet with M2 chip, 128GB storage, perfect for creative work.',
    price: 1099,
    category: 'Electronics',
    rating: 4.8,
    image_url: 'https://images.pexels.com/photos/1334597/pexels-photo-1334597.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 20,
    brand: 'Apple',
    specs: { processor: 'M2', storage: '128GB', display: '12.9" Liquid Retina' }
  },
  {
    id: 'watch-001',
    name: 'Apple Watch Series 9',
    description: 'Advanced smartwatch with health monitoring and fitness tracking.',
    price: 429,
    category: 'Electronics',
    rating: 4.6,
    image_url: 'https://images.pexels.com/photos/1038628/pexels-photo-1038628.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 35,
    brand: 'Apple',
    specs: { display: '45mm', features: 'Health monitoring, GPS' }
  },
  {
    id: 'camera-001',
    name: 'Canon EOS R5',
    description: 'Professional mirrorless camera with 45MP sensor and 8K video recording.',
    price: 3899,
    category: 'Electronics',
    rating: 4.9,
    image_url: 'https://images.pexels.com/photos/90946/pexels-photo-90946.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 8,
    brand: 'Canon',
    specs: { sensor: '45MP', video: '8K', type: 'Mirrorless' }
  },

  // Gaming
  {
    id: 'console-001',
    name: 'PlayStation 5',
    description: 'Next-gen gaming console with ultra-high speed SSD and ray tracing.',
    price: 499,
    category: 'Electronics',
    rating: 4.8,
    image_url: 'https://images.pexels.com/photos/371924/pexels-photo-371924.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: false,
    stock: 0,
    brand: 'Sony',
    specs: { storage: '825GB SSD', features: 'Ray tracing, 4K' }
  },
  {
    id: 'console-002',
    name: 'Xbox Series X',
    description: 'Most powerful Xbox ever with 12 teraflops of processing power.',
    price: 499,
    category: 'Electronics',
    rating: 4.7,
    image_url: 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 5,
    brand: 'Microsoft',
    specs: { storage: '1TB SSD', features: '12 teraflops, 4K' }
  },

  // More Books
  {
    id: 'book-005',
    name: 'The Psychology of Money',
    description: 'Timeless lessons on wealth, greed, and happiness from Morgan Housel.',
    price: 17.99,
    category: 'Books',
    rating: 4.7,
    image_url: 'https://images.pexels.com/photos/1148399/pexels-photo-1148399.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 45,
    brand: 'Harriman House',
    specs: { author: 'Morgan Housel', pages: 256, genre: 'Finance' }
  },
  {
    id: 'book-006',
    name: 'Educated',
    description: 'A memoir about education, transformation, and the power of learning.',
    price: 15.99,
    category: 'Books',
    rating: 4.6,
    image_url: 'https://images.pexels.com/photos/1481409/pexels-photo-1481409.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 30,
    brand: 'Random House',
    specs: { author: 'Tara Westover', pages: 334, genre: 'Memoir' }
  },

  // Kitchen & Home
  {
    id: 'coffee-001',
    name: 'Breville Barista Express',
    description: 'Espresso machine with built-in grinder for cafe-quality coffee at home.',
    price: 699,
    category: 'Home & Kitchen',
    rating: 4.5,
    image_url: 'https://images.pexels.com/photos/324028/pexels-photo-324028.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 15,
    brand: 'Breville',
    specs: { type: 'Espresso Machine', features: 'Built-in grinder' }
  },
  {
    id: 'blender-001',
    name: 'Vitamix 5200',
    description: 'Professional-grade blender for smoothies, soups, and more.',
    price: 449,
    category: 'Home & Kitchen',
    rating: 4.8,
    image_url: 'https://images.pexels.com/photos/434295/pexels-photo-434295.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 20,
    brand: 'Vitamix',
    specs: { power: '1380W', capacity: '64oz' }
  },

  // Fitness
  {
    id: 'fitness-001',
    name: 'Peloton Bike+',
    description: 'Interactive exercise bike with live and on-demand classes.',
    price: 2495,
    category: 'Fitness',
    rating: 4.4,
    image_url: 'https://images.pexels.com/photos/1552242/pexels-photo-1552242.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 5,
    brand: 'Peloton',
    specs: { display: '23.8" HD touchscreen', features: 'Live classes' }
  },
  {
    id: 'weights-001',
    name: 'Bowflex Adjustable Dumbbells',
    description: 'Space-saving adjustable dumbbells, 5-52.5 lbs per dumbbell.',
    price: 399,
    category: 'Fitness',
    rating: 4.6,
    image_url: 'https://images.pexels.com/photos/1552103/pexels-photo-1552103.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 12,
    brand: 'Bowflex',
    specs: { weight: '5-52.5 lbs', type: 'Adjustable' }
  },

  // Fashion
  {
    id: 'jacket-001',
    name: 'Patagonia Better Sweater Jacket',
    description: 'Sustainable fleece jacket made from recycled polyester.',
    price: 149,
    category: 'Fashion',
    rating: 4.7,
    image_url: 'https://images.pexels.com/photos/1183266/pexels-photo-1183266.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 25,
    brand: 'Patagonia',
    specs: { material: 'Recycled polyester', sizes: 'XS-XXL' }
  },
  {
    id: 'shoes-001',
    name: 'Nike Air Jordan 1 Retro High',
    description: 'Iconic basketball sneakers with premium leather construction.',
    price: 170,
    category: 'Fashion',
    rating: 4.8,
    image_url: 'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: true,
    stock: 18,
    brand: 'Nike',
    specs: { material: 'Leather', sizes: '7-13', color: 'Black/Red' }
  }
];

// Add more products to reach 100+ items
const additionalProducts: Product[] = Array.from({ length: 75 }, (_, index) => {
  const categories = ['Electronics', 'Books', 'Home & Kitchen', 'Fashion', 'Fitness'];
  const brands = ['Generic', 'Premium', 'Value', 'Pro', 'Elite'];
  const category = categories[index % categories.length];
  const brand = brands[index % brands.length];
  
  return {
    id: `product-${String(index + 100).padStart(3, '0')}`,
    name: `${brand} ${category} Item ${index + 1}`,
    description: `High-quality ${category.toLowerCase()} product with excellent features and modern design.`,
    price: Math.floor(Math.random() * 2000) + 50,
    category,
    rating: Math.round((Math.random() * 2 + 3) * 10) / 10,
    image_url: 'https://images.pexels.com/photos/1029141/pexels-photo-1029141.jpeg?auto=compress&cs=tinysrgb&w=500',
    availability: Math.random() > 0.1,
    stock: Math.floor(Math.random() * 50) + 1,
    brand,
    specs: { type: 'Standard', quality: 'High' }
  };
});

export const allProducts = [...mockProducts, ...additionalProducts];