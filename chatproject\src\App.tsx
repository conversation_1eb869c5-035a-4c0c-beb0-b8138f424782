import React, { useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import { useAuthStore } from './stores/useAuthStore';
import { useThemeStore } from './stores/useThemeStore';
import { AuthPage } from './components/Auth/AuthPage';
import { Header } from './components/Layout/Header';
import { Dashboard } from './components/Dashboard/Dashboard';
import { CartSidebar } from './components/Cart/CartSidebar';
import { FloatingChatButton } from './components/Layout/FloatingChatButton';

function App() {
  const { isAuthenticated } = useAuthStore();
  const { isDark } = useThemeStore();
  
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDark]);
  
  if (!isAuthenticated) {
    return <AuthPage />;
  }
  
  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'dark' : ''
    }`}>
      <Header />
      
      <main className="relative">
        <Dashboard />
      </main>
      
      {/* Cart Sidebar */}
      <CartSidebar />
      
      {/* Floating Chat Button */}
      <FloatingChatButton />
    </div>
  );
}

export default App;