import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { LoginForm } from './LoginForm';
import { RegisterForm } from './RegisterForm';
import { useThemeStore } from '../../stores/useThemeStore';

export const AuthPage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const isDark = useThemeStore(state => state.isDark);
  
  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'dark bg-gray-900' : 'bg-gradient-to-br from-blue-50 via-white to-emerald-50'
    }`}>
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 via-purple-400/20 to-emerald-400/20 backdrop-blur-3xl"></div>
      
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Branding */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            className="text-center lg:text-left"
          >
            <div className="mb-8">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.7 }}
                className="text-5xl lg:text-7xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600 bg-clip-text text-transparent mb-4"
              >
                ShopBot
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.7 }}
                className="text-xl lg:text-2xl text-gray-700 dark:text-gray-300 mb-6"
              >
                Your AI-Powered Shopping Assistant
              </motion.p>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.7 }}
                className="text-lg text-gray-600 dark:text-gray-400 max-w-md mx-auto lg:mx-0"
              >
                Discover amazing products with the help of our intelligent chatbot. 
                Find exactly what you need with personalized recommendations.
              </motion.p>
            </div>
            
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8, duration: 0.7 }}
              className="hidden lg:block"
            >
              <div className="grid grid-cols-2 gap-4 max-w-sm">
                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20">
                  <div className="text-3xl mb-2">🤖</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">AI Assistant</div>
                </div>
                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20">
                  <div className="text-3xl mb-2">🛍️</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Smart Shopping</div>
                </div>
                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20">
                  <div className="text-3xl mb-2">⚡</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Fast Search</div>
                </div>
                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/20">
                  <div className="text-3xl mb-2">💎</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Premium Quality</div>
                </div>
              </div>
            </motion.div>
          </motion.div>
          
          {/* Right side - Auth Forms */}
          <div className="flex justify-center">
            {isLogin ? (
              <LoginForm onToggleMode={() => setIsLogin(false)} />
            ) : (
              <RegisterForm onToggleMode={() => setIsLogin(true)} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};