{"name": "browserslist", "version": "4.24.0", "description": "Share target browsers between different front-end tools, like Autoprefixer, Stylelint and babel-env-preset", "keywords": ["caniuse", "browsers", "target"], "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "browserslist/browserslist", "dependencies": {"caniuse-lite": "^1.0.30001663", "electron-to-chromium": "^1.5.28", "node-releases": "^2.0.18", "update-browserslist-db": "^1.1.0"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "bin": {"browserslist": "cli.js"}, "types": "./index.d.ts", "browser": {"./node.js": "./browser.js", "path": false}}