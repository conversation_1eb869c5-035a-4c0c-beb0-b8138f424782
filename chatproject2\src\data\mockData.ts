
import { Product } from '@/types';

export const mockProducts: Product[] = [
  // Laptops
  {
    id: '1',
    name: 'MacBook Pro 16" M3',
    description: 'Apple MacBook Pro with M3 chip, 16GB RAM, 512GB SSD',
    price: 2499,
    category: 'Laptops',
    rating: 4.8,
    image_url: 'https://images.unsplash.com/photo-**********-5c52b6b3adef?w=400&h=300&fit=crop',
    availability: true,
    stock: 15,
    brand: 'Apple',
    discount: 5
  },
  {
    id: '2',
    name: 'Dell XPS 13',
    description: 'Ultra-thin laptop with Intel i7, 16GB RAM, 1TB SSD',
    price: 1299,
    category: 'Laptops',
    rating: 4.6,
    image_url: 'https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=400&h=300&fit=crop',
    availability: true,
    stock: 8,
    brand: 'Dell'
  },
  {
    id: '3',
    name: 'Gaming Laptop ASUS ROG',
    description: 'High-performance gaming laptop with RTX 4070, 32GB RAM',
    price: 1899,
    category: 'Laptops',
    rating: 4.7,
    image_url: 'https://images.unsplash.com/photo-1603302576837-37561b2e2302?w=400&h=300&fit=crop',
    availability: true,
    stock: 5,
    brand: 'ASUS'
  },

  // Smartphones
  {
    id: '4',
    name: 'iPhone 15 Pro',
    description: 'Latest iPhone with A17 Pro chip, 256GB storage',
    price: 999,
    category: 'Smartphones',
    rating: 4.9,
    image_url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop',
    availability: true,
    stock: 25,
    brand: 'Apple',
    discount: 3
  },
  {
    id: '5',
    name: 'Samsung Galaxy S24 Ultra',
    description: 'Premium Android phone with S Pen, 512GB storage',
    price: 1199,
    category: 'Smartphones',
    rating: 4.7,
    image_url: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=300&fit=crop',
    availability: true,
    stock: 12,
    brand: 'Samsung'
  },
  {
    id: '6',
    name: 'Google Pixel 8 Pro',
    description: 'AI-powered photography, pure Android experience',
    price: 899,
    category: 'Smartphones',
    rating: 4.5,
    image_url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop',
    availability: true,
    stock: 18,
    brand: 'Google'
  },

  // Headphones
  {
    id: '7',
    name: 'AirPods Pro 2',
    description: 'Active noise cancellation, spatial audio',
    price: 249,
    category: 'Audio',
    rating: 4.6,
    image_url: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c7faa0?w=400&h=300&fit=crop',
    availability: true,
    stock: 30,
    brand: 'Apple'
  },
  {
    id: '8',
    name: 'Sony WH-1000XM5',
    description: 'Industry-leading noise cancellation headphones',
    price: 399,
    category: 'Audio',
    rating: 4.8,
    image_url: 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=300&fit=crop',
    availability: true,
    stock: 20,
    brand: 'Sony'
  },
  {
    id: '9',
    name: 'Bose QuietComfort 45',
    description: 'Premium comfort with excellent noise cancellation',
    price: 329,
    category: 'Audio',
    rating: 4.5,
    image_url: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400&h=300&fit=crop',
    availability: true,
    stock: 15,
    brand: 'Bose'
  },

  // Tablets
  {
    id: '10',
    name: 'iPad Pro 12.9"',
    description: 'M2 chip, 256GB, perfect for creative professionals',
    price: 1099,
    category: 'Tablets',
    rating: 4.7,
    image_url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop',
    availability: true,
    stock: 10,
    brand: 'Apple'
  },
  {
    id: '11',
    name: 'Samsung Galaxy Tab S9',
    description: 'Android tablet with S Pen, 11" display',
    price: 799,
    category: 'Tablets',
    rating: 4.4,
    image_url: 'https://images.unsplash.com/photo-1561154464-82e9adf32764?w=400&h=300&fit=crop',
    availability: true,
    stock: 7,
    brand: 'Samsung'
  },

  // Cameras
  {
    id: '12',
    name: 'Canon EOS R6 Mark II',
    description: 'Professional mirrorless camera with 24.2MP sensor',
    price: 2499,
    category: 'Cameras',
    rating: 4.9,
    image_url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop',
    availability: true,
    stock: 4,
    brand: 'Canon'
  },
  {
    id: '13',
    name: 'Sony A7 IV',
    description: 'Full-frame mirrorless with 33MP resolution',
    price: 2198,
    category: 'Cameras',
    rating: 4.8,
    image_url: 'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=300&fit=crop',
    availability: true,
    stock: 6,
    brand: 'Sony'
  },

  // Gaming
  {
    id: '14',
    name: 'PlayStation 5',
    description: 'Latest gaming console with ultra-fast SSD',
    price: 499,
    category: 'Gaming',
    rating: 4.8,
    image_url: 'https://images.unsplash.com/photo-1607853202273-797f1c22a38e?w=400&h=300&fit=crop',
    availability: true,
    stock: 3,
    brand: 'Sony'
  },
  {
    id: '15',
    name: 'Xbox Series X',
    description: 'Powerful gaming console with 12 teraflops',
    price: 499,
    category: 'Gaming',
    rating: 4.7,
    image_url: 'https://images.unsplash.com/photo-1621259182978-fbf93132d53d?w=400&h=300&fit=crop',
    availability: true,
    stock: 5,
    brand: 'Microsoft'
  },

  // Smart Home
  {
    id: '16',
    name: 'Amazon Echo Dot 5th Gen',
    description: 'Smart speaker with Alexa voice assistant',
    price: 49,
    category: 'Smart Home',
    rating: 4.3,
    image_url: 'https://images.unsplash.com/photo-1518444065439-e933c06ce9cd?w=400&h=300&fit=crop',
    availability: true,
    stock: 50,
    brand: 'Amazon'
  },
  {
    id: '17',
    name: 'Google Nest Hub',
    description: 'Smart display with Google Assistant',
    price: 99,
    category: 'Smart Home',
    rating: 4.4,
    image_url: 'https://images.unsplash.com/photo-**********-3c8c76ca7d13?w=400&h=300&fit=crop',
    availability: true,
    stock: 25,
    brand: 'Google'
  },

  // Accessories
  {
    id: '18',
    name: 'Apple Watch Series 9',
    description: 'Advanced health monitoring and fitness tracking',
    price: 399,
    category: 'Wearables',
    rating: 4.6,
    image_url: 'https://images.unsplash.com/photo-1579586337278-3f436f25d4c8?w=400&h=300&fit=crop',
    availability: true,
    stock: 22,
    brand: 'Apple'
  },
  {
    id: '19',
    name: 'Samsung Galaxy Watch 6',
    description: 'Comprehensive health tracking with Android integration',
    price: 329,
    category: 'Wearables',
    rating: 4.4,
    image_url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=300&fit=crop',
    availability: true,
    stock: 15,
    brand: 'Samsung'
  },

  // More products to reach 100+
  {
    id: '20',
    name: 'MacBook Air M2',
    description: 'Lightweight laptop with M2 chip, perfect for students',
    price: 1199,
    category: 'Laptops',
    rating: 4.7,
    image_url: 'https://images.unsplash.com/photo-**********-5c52b6b3adef?w=400&h=300&fit=crop',
    availability: true,
    stock: 12,
    brand: 'Apple'
  }
  // ... Additional products would continue here to reach 100+ items
];

export const categories = [
  'All',
  'Laptops',
  'Smartphones',
  'Audio',
  'Tablets',
  'Cameras',
  'Gaming',
  'Smart Home',
  'Wearables'
];

export const brands = [
  'All',
  'Apple',
  'Samsung',
  'Sony',
  'Dell',
  'ASUS',
  'Google',
  'Canon',
  'Microsoft',
  'Amazon',
  'Bose'
];
