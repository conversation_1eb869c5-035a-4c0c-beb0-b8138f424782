
@tailwind base;
@tailwind components;
@tailwind utilities;

/* E-commerce Design System */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 215 25% 27%;

    --muted: 220 14% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;

    --radius: 0.75rem;

    /* Custom e-commerce colors */
    --ecommerce-primary: 217 91% 60%;
    --ecommerce-secondary: 142 76% 36%;
    --ecommerce-accent: 45 93% 47%;
    --ecommerce-warning: 25 95% 53%;
    --ecommerce-success: 142 76% 36%;
    --ecommerce-gradient-start: 217 91% 60%;
    --ecommerce-gradient-end: 142 76% 36%;
    
    --chat-user: 217 91% 60%;
    --chat-bot: 220 14% 96%;
    --chat-user-text: 0 0% 100%;
    --chat-bot-text: 215 25% 27%;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 0 0% 100%;

    --card: 215 28% 17%;
    --card-foreground: 0 0% 100%;

    --popover: 215 28% 17%;
    --popover-foreground: 0 0% 100%;

    --primary: 217 91% 60%;
    --primary-foreground: 215 28% 17%;

    --secondary: 215 28% 25%;
    --secondary-foreground: 0 0% 100%;

    --muted: 215 28% 25%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 91% 60%;
    --accent-foreground: 215 28% 17%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 28% 25%;
    --input: 215 28% 25%;
    --ring: 217 91% 60%;

    --chat-user: 217 91% 60%;
    --chat-bot: 215 28% 25%;
    --chat-user-text: 0 0% 100%;
    --chat-bot-text: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--ecommerce-gradient-start)), hsl(var(--ecommerce-gradient-end)));
  }
  
  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
  }
  
  .chat-user {
    background: hsl(var(--chat-user));
    color: hsl(var(--chat-user-text));
  }
  
  .chat-bot {
    background: hsl(var(--chat-bot));
    color: hsl(var(--chat-bot-text));
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}
